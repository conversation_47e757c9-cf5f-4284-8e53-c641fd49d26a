name: Auto-trigger Vercel Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    types: [ synchronize, opened, reopened, ready_for_review ]

permissions:
  contents: write

env:
  OWNER_NAME: 'Tech Local Accounts'
  OWNER_EMAIL: '<EMAIL>'

jobs:
  trigger-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Check out correct branch
        uses: actions/checkout@v3
        with:
          persist-credentials: true
          fetch-depth: 0
          ref: ${{ github.event_name == 'pull_request' && github.head_ref || github.ref }}

      - name: Get last relevant commit author email
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            # Ignore the temporary merge commit that GitHub creates for PRs
            LAST_EMAIL=$(git log -1 --no-merges --pretty=format:'%ae')
          else
            # For pushes to main (including merge commits), inspect the latest commit as-is
            LAST_EMAIL=$(git log -1 --pretty=format:'%ae')
          fi
          echo "LAST_EMAIL=$LAST_EMAIL" >> $GITHUB_ENV

      - name: Append dummy line and push as owner
        if: env.LAST_EMAIL != env.OWNER_EMAIL
        run: |
          # Ensure each entry starts on its own line
          printf "\n- CI trigger at %s\n" "$(date --utc)" >> trigger-deploy.md

          # Impersonate the owner
          git config user.name "${{ env.OWNER_NAME }}"
          git config user.email "${{ env.OWNER_EMAIL }}"

          # Commit & push back to this branch
          git add trigger-deploy.md
          git commit -m "chore: trigger Vercel deploy [auto]" || exit 0
          git push origin HEAD