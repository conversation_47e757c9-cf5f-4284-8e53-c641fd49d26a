
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

export const SUPABASE_URL = "https://szmyrzthodwvtzcnppdw.supabase.co";
export const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN6bXlyenRob2R3dnR6Y25wcGR3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyNDU2MTMsImV4cCI6MjA1ODgyMTYxM30.ADARWBSY2_tCt6k1FQ-f2rY-nA6mLrlzAbXHnWbBAtY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);
