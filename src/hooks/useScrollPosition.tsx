import { useState, useEffect } from 'react';

interface ScrollPosition {
  scrollY: number;
  isScrolled: boolean;
  isAtSection: (sectionId: string) => boolean;
  isPastSection: (sectionId: string) => boolean;
  isNavbarAtHeroBottom: () => boolean;
}

export function useScrollPosition(): ScrollPosition {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
      setIsScrolled(window.scrollY > 10);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Call once to set initial values
    handleScroll();

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Function to check if we've scrolled to a specific section
  const isAtSection = (sectionId: string): boolean => {
    const section = document.getElementById(sectionId);
    if (!section) return false;

    const sectionTop = section.offsetTop;
    const sectionHeight = section.offsetHeight;

    // Consider we're at a section when we've scrolled to its top
    // with a small buffer (100px) to trigger slightly before reaching it
    return scrollY >= sectionTop - 100;
  };

  // Function to check if we've scrolled past a specific section
  const isPastSection = (sectionId: string): boolean => {
    const section = document.getElementById(sectionId);
    if (!section) return false;

    const sectionTop = section.offsetTop;
    const sectionHeight = section.offsetHeight;

    return scrollY > sectionTop + sectionHeight;
  };

  // Function to check if the navbar bottom aligns with the hero section bottom
  const isNavbarAtHeroBottom = (): boolean => {
    const hero = document.getElementById('hero'); // Get the hero section by ID
    const navbarHeight = 80; // Height of the navbar (h-20 = 80px)

    if (!hero) return false;

    const heroBottom = hero.offsetTop + hero.offsetHeight;

    // The navbar bottom position is the current scroll position + navbar height
    // We want this to align with the hero bottom
    return scrollY + navbarHeight >= heroBottom;
  };

  return { scrollY, isScrolled, isAtSection, isPastSection, isNavbarAtHeroBottom };
}
