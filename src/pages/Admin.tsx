
import { useState, useEffect } from "react";
import { useNavigate, Navigate } from "react-router-dom";
import AdminLogin from "@/components/AdminLogin";
import { supabase } from "@/integrations/supabase/client";

const Admin = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if the user is already authenticated
    const checkAuth = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        setIsAuthenticated(true);
      }
      setIsLoading(false);
    };

    checkAuth();

    // Listen for authentication state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event) => {
        if (event === 'SIGNED_IN') {
          setIsAuthenticated(true);
        } else if (event === 'SIGNED_OUT') {
          setIsAuthenticated(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const handleBack = () => {
    navigate('/');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-techlocal-beige flex flex-col items-center justify-center">
        <p className="text-techlocal-dark font-dm-serif text-xl">Loading...</p>
      </div>
    );
  }

  // If authenticated, redirect to the CRM dashboard
  if (isAuthenticated) {
    return <Navigate to="/admin/dashboard" replace />;
  }

  return (
    <div className="min-h-screen bg-techlocal-beige flex flex-col">
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <h1 className="text-xl font-bold font-dm-serif text-techlocal-dark">Tech Local Administration</h1>
          <button
            onClick={handleBack}
            className="px-4 py-2 text-techlocal-dark hover:text-black transition-colors"
          >
            Back to Site
          </button>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8">
        <AdminLogin />
      </main>
    </div>
  );
};

export default Admin;
