import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Admin from "./pages/Admin";
import AdminCrm from "./pages/AdminCrm";
import NotFound from "./pages/NotFound";
import { SpeedInsights } from '@vercel/speed-insights/react';
import { Analytics } from '@vercel/analytics/react';

// CRM Components
import Dashboard from "./components/crm/Dashboard";
import Customers from "./components/crm/Customers";
import SalesPipeline from "./components/crm/SalesPipeline";
import Projects from "./components/crm/Projects";
import Quotes from "./components/crm/Quotes";
import Invoices from "./components/crm/Invoices";
import Settings from "./components/crm/Settings";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          {/* Admin Login Route */}
          <Route path="/admin" element={<Admin />} />

          {/* CRM Routes - Protected by AdminCrm component */}
          <Route path="/admin" element={<AdminCrm />}>
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="customers" element={<Customers />} />
            <Route path="sales" element={<SalesPipeline />} />
            <Route path="projects" element={<Projects />} />
            <Route path="quotes" element={<Quotes />} />
            <Route path="invoices" element={<Invoices />} />
            <Route path="settings" element={<Settings />} />
          </Route>

          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
    <SpeedInsights />
    <Analytics />
  </QueryClientProvider>
);

export default App;
