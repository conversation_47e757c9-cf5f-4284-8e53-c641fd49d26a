
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LogOut, Users, FileText, <PERSON>ting<PERSON>, <PERSON><PERSON><PERSON>, Folder<PERSON>anban, Receipt } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Link } from "react-router-dom";

const AdminDashboard = () => {
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      toast({
        title: "Signed out",
        description: "You have been signed out successfully",
      });
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-dm-serif font-bold text-techlocal-dark">Admin Dashboard</h1>
        <Button variant="outline" onClick={handleLogout} className="flex items-center gap-2 text-techlocal-dark border-techlocal-dark hover:bg-techlocal-beige/50">
          <LogOut size={16} />
          Logout
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <Users size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">Leads</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Manage potential sales</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">36</p>
            <p className="text-sm text-techlocal-dark/60">Possible clients</p>
            <Link to="/admin/customers">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                Manage Leads
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <FileText size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">New Project Requests</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Manage incoming project requests</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">12</p>
            <p className="text-sm text-techlocal-dark/60">Unprocessed enquiries</p>
            <Link to="/admin/sales">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                View All
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <Settings size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">Projects in Progress</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Configure system settings</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">3</p>
            <p className="text-sm text-techlocal-dark/60">Ongoing projects</p>
            <Link to="/admin/projects">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                Manage Projects
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <LineChart size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">Sales Pipeline</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Manage your sales</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">6</p>
            <p className="text-sm text-techlocal-dark/60">Active deals</p>
            <Link to="/admin/sales">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                View Pipeline
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <FileText size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">Quotes</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Manage quotes</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">3</p>
            <p className="text-sm text-techlocal-dark/60">Active quotes</p>
            <Link to="/admin/quotes">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                Manage Quotes
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-md border-0 hover:shadow-xl transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-techlocal-beige rounded-full w-12 h-12 flex items-center justify-center mb-4">
              <Receipt size={20} className="text-techlocal-dark" />
            </div>
            <CardTitle className="font-dm-serif text-techlocal-dark">Invoices</CardTitle>
            <CardDescription className="text-techlocal-dark/70">Manage invoices</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-techlocal-dark">3</p>
            <p className="text-sm text-techlocal-dark/60">Active invoices</p>
            <Link to="/admin/invoices">
              <Button className="w-full mt-4 bg-techlocal-dark hover:bg-techlocal-dark/80 text-white">
                Manage Invoices
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <Card className="shadow-md border-0">
        <CardHeader>
          <CardTitle className="font-dm-serif text-techlocal-dark">Quick Access</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link to="/admin/dashboard" className="w-full">
              <Button variant="outline" className="w-full justify-start">
                <Settings className="mr-2 h-4 w-4" />
                CRM Dashboard
              </Button>
            </Link>
            <Link to="/admin/customers" className="w-full">
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                Customer Management
              </Button>
            </Link>
            <Link to="/admin/projects" className="w-full">
              <Button variant="outline" className="w-full justify-start">
                <FolderKanban className="mr-2 h-4 w-4" />
                Project Management
              </Button>
            </Link>
            <Link to="/admin/sales" className="w-full">
              <Button variant="outline" className="w-full justify-start">
                <LineChart className="mr-2 h-4 w-4" />
                Sales Pipeline
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDashboard;
