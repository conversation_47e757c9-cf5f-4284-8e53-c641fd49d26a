import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CheckCircle2, Clock, Ban, AlertTriangle, Archive } from "lucide-react";

type InvoiceStatusType = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'archived';
type QuoteStatusType = 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired' | 'archived';
type StatusType = InvoiceStatusType | QuoteStatusType;

interface StatusChangeDropdownProps {
  currentStatus: StatusType;
  onStatusChange: (newStatus: StatusType) => void;
  entityType: 'invoice' | 'quote';
}

const StatusChangeDropdown: React.FC<StatusChangeDropdownProps> = ({
  currentStatus,
  onStatusChange,
  entityType,
}) => {
  // Define available status transitions based on current status
  const getAvailableStatuses = (): StatusType[] => {
    // For invoices
    if (entityType === 'invoice') {
      switch (currentStatus) {
        case 'draft':
          return ['sent', 'cancelled'];
        case 'sent':
          // Can't go back to draft once sent
          return ['paid', 'overdue', 'cancelled'];
        case 'paid':
          return ['sent', 'overdue', 'cancelled'];
        case 'overdue':
          return ['sent', 'paid', 'cancelled'];
        case 'cancelled':
          return ['draft', 'sent'];
        case 'archived':
          // Archived is a terminal state
          return [];
        default:
          return [];
      }
    }
    // For quotes
    else if (entityType === 'quote') {
      switch (currentStatus) {
        case 'draft':
          return ['sent', 'cancelled', 'archived'];
        case 'sent':
          // Can't go back to draft once sent
          return ['accepted', 'rejected', 'expired', 'archived'];
        case 'accepted':
          return ['sent', 'rejected', 'expired', 'archived'];
        case 'rejected':
          return ['sent', 'accepted', 'expired', 'archived'];
        case 'expired':
          return ['sent', 'accepted', 'rejected', 'archived'];
        case 'archived':
          // Archived is a terminal state
          return [];
        default:
          return [];
      }
    }
    // Default case
    else {
      return [];
    }
  };

  const getStatusIcon = (status: StatusType) => {
    switch (status) {
      // Invoice statuses
      case 'draft':
        return <Clock className="h-4 w-4 mr-2 text-gray-600" />;
      case 'sent':
        return <Clock className="h-4 w-4 mr-2 text-blue-600" />;
      case 'paid':
        return <CheckCircle2 className="h-4 w-4 mr-2 text-green-600" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />;
      case 'cancelled':
        return <Ban className="h-4 w-4 mr-2 text-amber-600" />;

      // Quote statuses
      case 'accepted':
        return <CheckCircle2 className="h-4 w-4 mr-2 text-green-600" />;
      case 'rejected':
        return <Ban className="h-4 w-4 mr-2 text-red-600" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4 mr-2 text-amber-600" />;

      // Common statuses
      case 'archived':
        return <Archive className="h-4 w-4 mr-2 text-purple-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: StatusType) => {
    switch (status) {
      // Invoice statuses
      case 'draft':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      case 'sent':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'paid':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'cancelled':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-200';

      // Quote statuses
      case 'accepted':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'expired':
        return 'bg-amber-100 text-amber-800 hover:bg-amber-200';

      // Common statuses
      case 'archived':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const formatStatusTitle = (status: string) => {
    // For invoices, show "Due" for draft and sent statuses
    if (entityType === 'invoice' && (status === 'draft' || status === 'sent')) {
      return 'Due';
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const availableStatuses = getAvailableStatuses();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium cursor-pointer ${getStatusColor(currentStatus)}`}
        >
          {formatStatusTitle(currentStatus)}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center" className="w-40">
        {availableStatuses.map((status) => (
          <DropdownMenuItem
            key={status}
            onClick={() => onStatusChange(status)}
            className="cursor-pointer"
          >
            {getStatusIcon(status)}
            {formatStatusTitle(status)}
          </DropdownMenuItem>
        ))}
        {availableStatuses.length === 0 && (
          <DropdownMenuItem disabled>
            No status changes available
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusChangeDropdown;
