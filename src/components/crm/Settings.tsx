import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { useState, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import {
  User,
  Mail,
  Building,
  Phone,
  Globe,
  CreditCard,
  Bell,
  Shield,
  Users
} from "lucide-react";
import CrmLayout from "./CrmLayout";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { companyInfo } from "@/lib/companyInfo";

const profileFormSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  contactName: z.string().min(1, "Contact name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  address: z.string().optional(),
  website: z.string().optional(),
  logo: z.string().optional(),
});

const notificationFormSchema = z.object({
  emailNotifications: z.boolean(),
  newLeadNotifications: z.boolean(),
  quoteNotifications: z.boolean(),
  invoiceNotifications: z.boolean(),
  projectNotifications: z.boolean(),
});

const emailTemplateFormSchema = z.object({
  quoteEmailTemplate: z.string(),
  invoiceEmailTemplate: z.string(),
  welcomeEmailTemplate: z.string(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type NotificationFormValues = z.infer<typeof notificationFormSchema>;
type EmailTemplateFormValues = z.infer<typeof emailTemplateFormSchema>;

const Settings = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);

        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load user data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [toast]);

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      companyName: companyInfo.name,
      contactName: "Admin User",
      email: companyInfo.email,
      phone: companyInfo.phone,
      address: companyInfo.address,
      website: companyInfo.website,
      logo: "",
    }
  });

  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      emailNotifications: true,
      newLeadNotifications: true,
      quoteNotifications: true,
      invoiceNotifications: true,
      projectNotifications: true,
    }
  });

  const emailTemplateForm = useForm<EmailTemplateFormValues>({
    resolver: zodResolver(emailTemplateFormSchema),
    defaultValues: {
      quoteEmailTemplate: "Dear [Customer Name],\n\nPlease find attached your quote [Quote Number] for [Project Title].\n\nThe quote is valid until [Valid Until Date].\n\nPlease let us know if you have any questions.\n\nBest regards,\nTech Local Team",
      invoiceEmailTemplate: "Dear [Customer Name],\n\nPlease find attached your invoice [Invoice Number] for [Project Title].\n\nThe invoice is due on [Due Date].\n\nPlease let us know if you have any questions.\n\nBest regards,\nTech Local Team",
      welcomeEmailTemplate: "Dear [Customer Name],\n\nWelcome to Tech Local! We're excited to work with you on your project.\n\nYour account has been created and you can now access our client portal at [Portal URL].\n\nPlease let us know if you have any questions.\n\nBest regards,\nTech Local Team",
    }
  });

  const onProfileSubmit = async (data: ProfileFormValues) => {
    try {
      // In a real implementation, you would save this to a settings table in Supabase
      // For now, we'll just show a success message
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  const onNotificationSubmit = async (data: NotificationFormValues) => {
    try {
      // In a real implementation, you would save this to a settings table in Supabase
      // For now, we'll just show a success message
      toast({
        title: "Notification Settings Updated",
        description: "Your notification settings have been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating notification settings:", error);
      toast({
        title: "Error",
        description: "Failed to update notification settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  const onEmailTemplateSubmit = async (data: EmailTemplateFormValues) => {
    try {
      // In a real implementation, you would save this to a settings table in Supabase
      // For now, we'll just show a success message
      toast({
        title: "Email Templates Updated",
        description: "Your email templates have been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating email templates:", error);
      toast({
        title: "Error",
        description: "Failed to update email templates. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <CrmLayout
      title="Settings"
      breadcrumbs={[{ title: "Settings" }]}
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-lg text-gray-500">Loading settings data...</p>
        </div>
      ) : (
        <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="profile">Company Profile</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile">
          <Card className="shadow-sm border-0">
            <CardHeader>
              <CardTitle className="font-dm-serif text-techlocal-dark">Company Profile</CardTitle>
              <CardDescription>
                Manage your company information and branding
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...profileForm}>
                <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={profileForm.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company Name</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Building className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                              <Input {...field} className="pl-10" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="contactName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Person</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <User className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                              <Input {...field} className="pl-10" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                              <Input {...field} type="email" className="pl-10" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                              <Input {...field} className="pl-10" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                              <Input {...field} className="pl-10" />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="logo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo</FormLabel>
                          <FormControl>
                            <Input type="file" accept="image/*" onChange={(e) => {
                              // This would normally upload the file and set the URL
                              field.onChange(e.target.value);
                            }} />
                          </FormControl>
                          <FormDescription>
                            Upload your company logo (recommended size: 200x200px)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={profileForm.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Enter your company address"
                            className="min-h-[80px]"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                    >
                      Save Changes
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications">
          <Card className="shadow-sm border-0">
            <CardHeader>
              <CardTitle className="font-dm-serif text-techlocal-dark">Notification Settings</CardTitle>
              <CardDescription>
                Configure how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...notificationForm}>
                <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={notificationForm.control}
                      name="emailNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Email Notifications</FormLabel>
                            <FormDescription>
                              Receive notifications via email
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="newLeadNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">New Lead Notifications</FormLabel>
                            <FormDescription>
                              Get notified when a new lead is created
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={!notificationForm.watch("emailNotifications")}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="quoteNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Quote Notifications</FormLabel>
                            <FormDescription>
                              Get notified when a quote is accepted or rejected
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={!notificationForm.watch("emailNotifications")}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="invoiceNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Invoice Notifications</FormLabel>
                            <FormDescription>
                              Get notified when an invoice is paid or overdue
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={!notificationForm.watch("emailNotifications")}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="projectNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Project Notifications</FormLabel>
                            <FormDescription>
                              Get notified about project updates and milestones
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={!notificationForm.watch("emailNotifications")}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                    >
                      Save Changes
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Templates Tab */}
        <TabsContent value="templates">
          <Card className="shadow-sm border-0">
            <CardHeader>
              <CardTitle className="font-dm-serif text-techlocal-dark">Email Templates</CardTitle>
              <CardDescription>
                Customize the email templates sent to customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...emailTemplateForm}>
                <form onSubmit={emailTemplateForm.handleSubmit(onEmailTemplateSubmit)} className="space-y-6">
                  <FormField
                    control={emailTemplateForm.control}
                    name="quoteEmailTemplate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quote Email Template</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            className="min-h-[150px] font-mono text-sm"
                          />
                        </FormControl>
                        <FormDescription>
                          Available variables: [Customer Name], [Quote Number], [Project Title], [Valid Until Date]
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={emailTemplateForm.control}
                    name="invoiceEmailTemplate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Invoice Email Template</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            className="min-h-[150px] font-mono text-sm"
                          />
                        </FormControl>
                        <FormDescription>
                          Available variables: [Customer Name], [Invoice Number], [Project Title], [Due Date]
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={emailTemplateForm.control}
                    name="welcomeEmailTemplate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Welcome Email Template</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            className="min-h-[150px] font-mono text-sm"
                          />
                        </FormControl>
                        <FormDescription>
                          Available variables: [Customer Name], [Portal URL]
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                    >
                      Save Changes
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      )}
    </CrmLayout>
  );
};

export default Settings;
