import React from "react";
import { Quote, Customer } from "@/types/crm";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

interface QuotePreviewProps {
  quote: Quote;
  customer: Customer;
  companyInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
  };
}

const QuotePreview = React.forwardRef<HTMLDivElement, QuotePreviewProps>(
  ({ quote, customer, companyInfo }, ref) => {
    const depositAmount = quote.amount / 2;
    return (
      <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">QUOTE</h1>
            <p className="text-gray-600 mt-1">#{quote.id.substring(0, 6).toUpperCase()}</p>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold text-gray-900">{companyInfo.name}</h2>
            <p className="text-gray-600 whitespace-pre-line">{companyInfo.address}</p>
            <p className="text-gray-600">{companyInfo.phone}</p>
            <p className="text-gray-600">{companyInfo.email}</p>
            <p className="text-gray-600">{companyInfo.website}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-gray-500 font-medium mb-2">Customer Details:</h3>
            <p className="font-bold">{customer.name}</p>
            <p>{customer.contactName}</p>
            <p>{customer.email}</p>
            <p>{customer.contactNumber}</p>
            {customer.address && <p className="whitespace-pre-line">{customer.address}</p>}
          </div>
          <div className="text-right">
            <div>
              <span className="text-gray-500 font-medium">Issued:</span>
              <span className="ml-2">{format(new Date(quote.createdAt), "MMMM dd, yyyy")}</span>
            </div>
            <div>
              <span className="text-gray-500 font-medium">Valid Until:</span>
              <span className="ml-2">{format(new Date(quote.validUntil), "MMMM dd, yyyy")}</span>
            </div>
          </div>
        </div>

        <table className="w-full mb-8 border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="py-2 px-4 text-left border border-gray-200">Description</th>
              <th className="py-2 px-4 text-right border border-gray-200">Quantity</th>
              <th className="py-2 px-4 text-right border border-gray-200">Unit Price</th>
              <th className="py-2 px-4 text-right border border-gray-200">Total</th>
            </tr>
          </thead>
          <tbody>
            {quote.items.map((item, index) => (
              <tr key={index} className="border-b border-gray-200">
                <td className="py-2 px-4 border border-gray-200">{item.description}</td>
                <td className="py-2 px-4 text-right border border-gray-200">{item.quantity}</td>
                <td className="py-2 px-4 text-right border border-gray-200">{formatCurrency(item.unitPrice)}</td>
                <td className="py-2 px-4 text-right border border-gray-200">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan={3} className="py-2 px-4 text-right font-bold">Subtotal:</td>
              <td className="py-2 px-4 text-right border border-gray-200">{formatCurrency(quote.amount)}</td>
            </tr>
            <tr>
              <td colSpan={3} className="py-2 px-4 text-right font-bold">Total:</td>
              <td className="py-2 px-4 text-right border border-gray-200 font-bold">{formatCurrency(quote.amount)}</td>
            </tr>
          </tfoot>
        </table>

        <div className="mb-8">
          <h3 className="text-gray-500 font-medium mb-2">Notes:</h3>
          <p className="text-gray-600">
            Please contact us if you have any questions. Supplier not registered for VAT.
          </p>

          <p className="text-gray-600 mt-2">
            A 50% deposit of {formatCurrency(depositAmount)} is required for work to begin.
          </p>

          <h3 className="text-gray-500 font-medium mb-2 mt-4">Banking Details:</h3>
          <p className="text-gray-600">Bank: Capitec Business</p>
          <p className="text-gray-600">Account Name: Tech Local</p>
          <p className="text-gray-600">Account Type: Transact</p>
          <p className="text-gray-600">Account Number: **********</p>
          <p className="text-gray-600">Branch Code: 450105</p>
        </div>

        <div className="text-center text-gray-500 text-sm mt-16">
          <p>Thank you for your business!</p>
        </div>
      </div>
    );
  }
);

QuotePreview.displayName = "QuotePreview";

export default QuotePreview;
