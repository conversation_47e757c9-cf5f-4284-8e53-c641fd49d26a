import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Download, Mail, Eye, AlertTriangle } from "lucide-react";
import { Customer } from "@/types/crm";
import { useToast } from "@/hooks/use-toast";

export type DocumentType = 'quote' | 'invoice';

interface DocumentPreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: any; // Quote or Invoice
  customer: Customer;
  documentType: DocumentType;
  onDocumentUpdated?: () => void;
  initialTab?: "preview" | "download" | "email";
  // Service functions
  getCompanyInfo: () => any;
  generatePDF: (element: HTMLElement, document: any, customer: Customer) => Promise<string>;
  downloadPDF: (pdfBase64: string, documentId: string, document?: any) => Promise<void>;
  sendEmail: (document: any, customer: Customer, pdfBase64: string, message: string) => Promise<any>;
  updateStatus: (documentId: string, status: string) => Promise<void>;
  // Components
  PreviewComponent: React.ForwardRefExoticComponent<any>;
}

const DocumentPreviewDialog: React.FC<DocumentPreviewDialogProps> = ({
  open,
  onOpenChange,
  document,
  customer,
  documentType,
  onDocumentUpdated,
  initialTab = "preview",
  getCompanyInfo,
  generatePDF,
  downloadPDF,
  sendEmail,
  updateStatus,
  PreviewComponent,
}) => {
  const [activeTab, setActiveTab] = useState<"preview" | "download" | "email">(initialTab);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [emailMessage, setEmailMessage] = useState("");
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const companyInfo = getCompanyInfo();

  // Generate default email message
  useEffect(() => {
    if (open) {
      if (documentType === 'quote') {
        setEmailMessage(
          `Dear ${customer.name},\n\nPlease find attached your quote for ${document.title}.\n\nThe quote is valid until ${new Date(document.validUntil).toLocaleDateString()}.\n\nPlease let us know if you have any questions.\n\nBest regards,\nTech Local Team`
        );
      } else {
        setEmailMessage(
          `Dear ${customer.name},\n\nPlease find attached your invoice for ${document.title}.\n\nThe invoice is due on ${new Date(document.dueDate).toLocaleDateString()}.\n\nPlease let us know if you have any questions.\n\nBest regards,\nTech Local Team`
        );
      }
    }
  }, [open, customer.name, document, documentType]);

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setPdfBase64(null);
    }
  }, [open]);

  // Generate PDF when tab changes to email or download
  useEffect(() => {
    const generatePDFDocument = async () => {
      if ((activeTab === "email" || activeTab === "download") && !pdfBase64 && previewRef.current) {
        setIsGeneratingPDF(true);
        try {
          const base64 = await generatePDF(previewRef.current, document, customer);
          setPdfBase64(base64);
        } catch (error) {
          console.error(`Error generating ${documentType} PDF:`, error);
          toast({
            title: "Error",
            description: `Failed to generate PDF. Please try again.`,
            variant: "destructive",
          });
        } finally {
          setIsGeneratingPDF(false);
        }
      }
    };

    generatePDFDocument();
  }, [activeTab, pdfBase64, toast, document, customer, documentType, generatePDF]);

  const handleDownload = async () => {
    if (!pdfBase64) {
      setIsGeneratingPDF(true);
      try {
        if (previewRef.current) {
          const base64 = await generatePDF(previewRef.current, document, customer);
          setPdfBase64(base64);
          await downloadPDF(base64, document.id, document);
          
          // Refresh if status was changed
          if (document.status === "draft") {
            if (onDocumentUpdated) onDocumentUpdated();
          }
        }
      } catch (error) {
        console.error(`Error generating ${documentType} PDF:`, error);
        toast({
          title: "Error",
          description: "Failed to generate PDF. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsGeneratingPDF(false);
      }
    } else {
      await downloadPDF(pdfBase64, document.id, document);
      
      // Refresh if status was changed
      if (document.status === "draft") {
        if (onDocumentUpdated) onDocumentUpdated();
      }
    }
  };

  const handleSendEmail = async () => {
    if (!pdfBase64) {
      toast({
        title: "Error",
        description: "PDF is still generating. Please wait.",
        variant: "destructive",
      });
      return;
    }

    setIsSendingEmail(true);
    try {
      const result = await sendEmail(
        document,
        customer,
        pdfBase64,
        emailMessage
      );

      if (result.success) {
        // If the document is in draft status, update it to sent
        if (document.status === "draft") {
          try {
            await updateStatus(document.id, "sent");
            toast({
              title: `${documentType.charAt(0).toUpperCase() + documentType.slice(1)} Status Updated`,
              description: `${documentType.charAt(0).toUpperCase() + documentType.slice(1)} has been marked as sent.`,
            });
          } catch (error) {
            console.error(`Error updating ${documentType} status:`, error);
          }
        }
        
        toast({
          title: "Success",
          description: result.message,
        });
        onOpenChange(false);
        if (onDocumentUpdated) onDocumentUpdated();
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast({
        title: "Error",
        description: "Failed to send email. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Check if the document is immutable (not in draft status)
  const isImmutable = document.status !== 'draft';
  
  // Get document number for display
  const documentNumber = documentType === 'quote' 
    ? document.id.substring(0, 6).toUpperCase()
    : document.id.substring(0, 6).toUpperCase();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="font-dm-serif text-techlocal-dark">
            {documentType.charAt(0).toUpperCase() + documentType.slice(1)}: {document.title}
            {isImmutable && (
              <div className="flex items-center gap-2 mt-2 text-sm font-normal">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <span className="text-amber-600">This {documentType} is {document.status} and cannot be modified</span>
              </div>
            )}
          </DialogTitle>
          <DialogDescription>
            Preview, download or email this {documentType} to the customer
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "preview" | "download" | "email")} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="download">
              <Download className="mr-2 h-4 w-4" />
              Download
            </TabsTrigger>
            <TabsTrigger value="email" disabled={document.status === 'archived'}>
              <Mail className="mr-2 h-4 w-4" />
              Email
            </TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="mt-4">
            <div className="border rounded-md p-1 bg-gray-50">
              <PreviewComponent
                ref={previewRef}
                {...{[documentType]: document}}
                customer={customer}
                companyInfo={companyInfo}
              />
            </div>
          </TabsContent>

          <TabsContent value="download" className="mt-4">
            <div className="space-y-4">
              <div className="border rounded-md p-1 bg-gray-50 max-h-[400px] overflow-y-auto">
                <PreviewComponent
                  {...{[documentType]: document}}
                  customer={customer}
                  companyInfo={companyInfo}
                />
              </div>
              <Button
                onClick={handleDownload}
                disabled={isGeneratingPDF}
                className="w-full bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
              >
                {isGeneratingPDF ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating PDF...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Download {documentType.charAt(0).toUpperCase() + documentType.slice(1)} PDF
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="email" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="recipient">Recipient</Label>
                <div className="p-2 border rounded-md bg-gray-50">
                  {customer.name} ({customer.email})
                </div>
              </div>

              <div>
                <Label htmlFor="message">Email Message</Label>
                <Textarea
                  id="message"
                  value={emailMessage}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  className="min-h-[150px]"
                />
              </div>

              <div className="border rounded-md p-2 bg-gray-50">
                <h3 className="font-medium mb-2">Attachment</h3>
                <div className="flex items-center space-x-2 text-sm">
                  <Download className="h-4 w-4" />
                  <span>{documentType.charAt(0).toUpperCase() + documentType.slice(1)}-{documentNumber}.pdf</span>
                  {isGeneratingPDF && (
                    <Loader2 className="h-4 w-4 animate-spin ml-auto" />
                  )}
                </div>
              </div>

              <Button
                onClick={handleSendEmail}
                disabled={isGeneratingPDF || isSendingEmail || !pdfBase64}
                className="w-full bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
              >
                {isSendingEmail ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending Email...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Send {documentType.charAt(0).toUpperCase() + documentType.slice(1)} to Customer
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentPreviewDialog;
