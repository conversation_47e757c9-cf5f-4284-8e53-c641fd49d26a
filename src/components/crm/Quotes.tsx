import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Search,
  Plus,
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  User,
  Calendar,
  FileText,
  Receipt,
  Download,
  Mail,
  Eye,
  Copy,
  AlertTriangle
} from "lucide-react";
import CrmLayout from "./CrmLayout";
import QuotePreviewDialog from "./QuotePreviewDialog";
import StatusChangeDropdown from "./StatusChangeDropdown";
import { customerService, dealService, projectService, quoteService, invoiceService } from "@/services";
import { quoteUtilsService } from "@/services/quoteUtilsService";
import { Quote, QuoteItem, Customer, Deal, Project, Invoice } from "@/types/crm";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";

const quoteItemSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  unitPrice: z.coerce.number().min(0, "Unit price must be at least 0"),
  total: z.coerce.number().min(0, "Total must be at least 0"),
});

const quoteFormSchema = z.object({
  title: z.string().min(1, "Quote title is required"),
  customerId: z.string().min(1, "Customer is required"),
  dealId: z.string().optional(),
  projectId: z.string().optional(),
  status: z.enum(["draft", "sent", "accepted", "rejected", "expired", "archived"]),
  validUntil: z.string().min(1, "Valid until date is required"),
  items: z.array(quoteItemSchema).min(1, "At least one item is required"),
});

type QuoteFormValues = z.infer<typeof quoteFormSchema>;

const Quotes = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [activePreviewTab, setActivePreviewTab] = useState<"preview" | "download" | "email">("preview");
  const [isReviseDialogOpen, setIsReviseDialogOpen] = useState(false);
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch data in parallel
        const [quotesData, customersData, dealsData, projectsData] = await Promise.all([
          quoteService.getQuotes(),
          customerService.getCustomers(),
          dealService.getDeals(),
          projectService.getProjects()
        ]);

        setQuotes(quotesData);
        setCustomers(customersData);
        setDeals(dealsData);
        setProjects(projectsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load quotes data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Function to get a date 1 week from today in YYYY-MM-DD format
  const getOneWeekFromToday = () => {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date.toISOString().split('T')[0];
  };

  const form = useForm<QuoteFormValues>({
    resolver: zodResolver(quoteFormSchema),
    defaultValues: {
      title: "",
      customerId: "",
      dealId: "none",
      projectId: "none",
      status: "draft",
      validUntil: getOneWeekFromToday(),
      items: [
        {
          description: "",
          quantity: 1,
          unitPrice: 0,
          total: 0,
        }
      ],
    }
  });

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch =
      quote.title.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || quote.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleAddQuote = async (data: QuoteFormValues) => {
    try {
      // Prepare the data for submission
      const quoteData = {
        title: data.title,
        customerId: data.customerId,
        status: data.status,
        validUntil: data.validUntil,
        // Convert 'none' to null for optional fields
        dealId: data.dealId === 'none' ? null : data.dealId,
        projectId: data.projectId === 'none' ? null : data.projectId,
        items: data.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.quantity * item.unitPrice
        }))
      };
      if (selectedQuote) {
        // Update existing quote
        await quoteService.updateQuote(selectedQuote.id, quoteData);
        toast({
          title: "Quote Updated",
          description: `${data.title} has been updated successfully.`,
        });
      } else {
        // Create new quote
        await quoteService.createQuote(quoteData);
        toast({
          title: "Quote Added",
          description: `${data.title} has been added successfully.`,
        });
      }

      // Refresh quote list
      const updatedQuotes = await quoteService.getQuotes();
      setQuotes(updatedQuotes);

      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error saving quote:", error);
      toast({
        title: "Error",
        description: "Failed to save quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditQuote = (quote: Quote) => {
    // Only allow direct editing of draft quotes
    if (quote.status !== 'draft') {
      toast({
        title: "Cannot Edit",
        description: "Only draft quotes can be edited directly. Please use 'Revise Quote' for non-draft quotes.",
        variant: "destructive",
      });
      return;
    }

    setSelectedQuote(quote);
    form.reset({
      title: quote.title,
      customerId: quote.customerId,
      dealId: quote.dealId || "none",
      projectId: quote.projectId || "none",
      status: quote.status,
      validUntil: new Date(quote.validUntil).toISOString().split('T')[0],
      items: quote.items,
    });
    setIsAddDialogOpen(true);
  };

  const handleDeleteQuote = (quote: Quote) => {
    setSelectedQuote(quote);
    setIsDeleteDialogOpen(true);
  };

  const handlePreviewQuote = (quote: Quote, initialTab: "preview" | "download" | "email" = "preview") => {
    setActivePreviewTab(initialTab);
    setSelectedQuote(quote);
    const customer = customers.find(c => c.id === quote.customerId);
    if (customer) {
      setSelectedCustomer(customer);
      setIsPreviewDialogOpen(true);
      // Close the edit dialog if it's open
      setIsAddDialogOpen(false);
    } else {
      toast({
        title: "Error",
        description: "Customer information not found.",
        variant: "destructive",
      });
    }
  };

  // Handler for directly downloading a quote PDF without opening the preview dialog
  const handleDownloadQuote = async (quote: Quote) => {
    try {
      // Find the customer for this quote
      const customer = customers.find(c => c.id === quote.customerId);
      if (!customer) {
        toast({
          title: "Error",
          description: "Customer information not found.",
          variant: "destructive",
        });
        return;
      }

      // Show loading toast
      toast({
        title: "Generating PDF",
        description: "Please wait while we generate your PDF...",
      });

      // Get company info and generate the PDF directly
      // We don't need the HTML element reference for direct PDF generation
      // The first parameter is kept for compatibility but not used in the implementation
      const pdfBase64 = await quoteUtilsService.generatePDF(document.createElement('div'), quote, customer);

      // Download the PDF and handle status change if needed
      await quoteUtilsService.downloadQuotePDF(pdfBase64, quote.id, quote);

      // Refresh quotes list in case the status was changed
      const updatedQuotes = await quoteService.getQuotes();
      setQuotes(updatedQuotes);

      // Show success toast
      toast({
        title: "Success",
        description: "PDF downloaded successfully.",
      });
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    }
  };

  const confirmDelete = async () => {
    if (selectedQuote) {
      try {
        await quoteService.deleteQuote(selectedQuote.id);
        toast({
          title: "Quote Deleted",
          description: `${selectedQuote.title} has been deleted.`,
        });

        // Refresh quote list
        const updatedQuotes = await quoteService.getQuotes();
        setQuotes(updatedQuotes);
      } catch (error) {
        console.error("Error deleting quote:", error);
        toast({
          title: "Error",
          description: "Failed to delete quote. Please try again.",
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
    setSelectedQuote(null);
  };

  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  // Function to navigate to customer edit page
  const handleViewCustomer = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      // Navigate to the customer page and open the edit modal
      window.location.href = `/admin/customers?edit=${customer.id}`;
    }
  };

  const getStatusColor = (status: Quote['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-amber-100 text-amber-800';
      case 'archived':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handler for revising a quote
  const handleReviseQuote = (quote: Quote) => {
    setSelectedQuote(quote);
    setIsReviseDialogOpen(true);
  };

  // Confirm quote revision
  const confirmReviseQuote = async () => {
    if (!selectedQuote) return;

    try {
      // Create a new quote as a revision of the selected quote
      const revisedQuote = await quoteService.createQuoteRevision(selectedQuote.id);

      toast({
        title: "Quote Revised",
        description: `The original quote has been archived and a new draft quote has been created.`,
      });

      // Refresh quotes list
      const updatedQuotes = await quoteService.getQuotes();
      setQuotes(updatedQuotes);

      // Close the dialog
      setIsReviseDialogOpen(false);

      // Open the edit dialog for the new quote
      handleEditQuote(revisedQuote);
    } catch (error) {
      console.error("Error revising quote:", error);
      toast({
        title: "Error",
        description: "Failed to revise quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatStatusTitle = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Function to add a new quote item
  const addQuoteItem = () => {
    const items = form.getValues("items");
    form.setValue("items", [
      ...items,
      {
        description: "",
        quantity: 1,
        unitPrice: 0,
        total: 0,
      }
    ]);
  };

  // Function to remove a quote item
  const removeQuoteItem = (index: number) => {
    const items = form.getValues("items");
    if (items.length > 1) {
      form.setValue("items", items.filter((_, i) => i !== index));
    }
  };

  // Function to update the total when quantity or unit price changes
  const updateItemTotal = (index: number) => {
    const items = form.getValues("items");
    const item = items[index];
    const total = item.quantity * item.unitPrice;
    form.setValue(`items.${index}.total`, total);
  };

  // Calculate quote total
  const calculateQuoteTotal = (items: { total?: number }[]) => {
    return items.reduce((sum, item) => sum + (item.total || 0), 0);
  };

  return (
    <CrmLayout
      title="Quotes"
      breadcrumbs={[{ title: "Quotes" }]}
    >
      <Card className="shadow-sm border-0">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="font-dm-serif text-techlocal-dark">Quote Management</CardTitle>
              <CardDescription>Manage your quotes and proposals</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder="Search quotes..."
                  className="pl-8 w-full sm:w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => {
                  form.reset({
                    title: "",
                    customerId: "",
                    dealId: "none",
                    projectId: "none",
                    status: "draft",
                    validUntil: getOneWeekFromToday(),
                    items: [
                      {
                        description: "",
                        quantity: 1,
                        unitPrice: 0,
                        total: 0,
                      }
                    ],
                  });
                  setSelectedQuote(null);
                  setIsAddDialogOpen(true);
                }}
                className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                New Quote
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quote Title</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      Loading quotes...
                    </TableCell>
                  </TableRow>
                ) : filteredQuotes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No quotes found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredQuotes.map((quote) => (
                    <TableRow
                      key={quote.id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={(e) => {
                        // Prevent row click if clicking on status tag or dropdown
                        if (
                          e.target instanceof HTMLElement &&
                          (e.target.closest('.status-tag') ||
                           e.target.closest('.dropdown-trigger') ||
                           e.target.closest('.customer-link'))
                        ) {
                          return;
                        }
                        if (quote.status === 'draft') {
                          handleEditQuote(quote);
                        } else {
                          handlePreviewQuote(quote);
                        }
                      }}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <span className="text-left hover:text-techlocal-dark">
                            {quote.title}
                          </span>
                          {quote.parentQuoteId && (
                            <span className="text-xs text-gray-500">(Revised)</span>
                          )}
                          {quote.status === 'archived' && (
                            <span className="text-xs text-purple-600 font-medium">(Archived)</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewCustomer(quote.customerId);
                          }}
                          className="text-left hover:text-techlocal-dark hover:underline customer-link"
                        >
                          {getCustomerName(quote.customerId)}
                        </button>
                      </TableCell>
                      <TableCell>{formatCurrency(quote.amount)}</TableCell>
                      <TableCell>
                        {quote.status === 'archived' ? (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-tag ${getStatusColor(quote.status)}`}>
                            {formatStatusTitle(quote.status)}
                          </span>
                        ) : (
                          <div onClick={(e) => e.stopPropagation()} className="status-tag">
                            <StatusChangeDropdown
                              currentStatus={quote.status}
                              entityType="quote"
                              onStatusChange={async (newStatus: Quote['status']) => {
                                try {
                                  // Update the quote status
                                  await quoteService.updateQuote(quote.id, { status: newStatus });

                                  // Refresh quote list
                                  const updatedQuotes = await quoteService.getQuotes();
                                  setQuotes(updatedQuotes);

                                  toast({
                                    title: "Status Updated",
                                    description: `Quote status changed to ${formatStatusTitle(newStatus)}.`,
                                  });
                                } catch (error) {
                                  console.error("Error updating quote status:", error);
                                  toast({
                                    title: "Error",
                                    description: "Failed to update quote status. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            />
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(quote.validUntil).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-8 w-8 p-0 dropdown-trigger"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditQuote(quote);
                              }}
                              disabled={quote.status !== 'draft'}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            {quote.status !== 'draft' && quote.status !== 'archived' && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleReviseQuote(quote);
                                }}
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Revise Quote
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteQuote(quote);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>

                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreviewQuote(quote);
                              }}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Preview Quote
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadQuote(quote);
                              }}
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download PDF
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreviewQuote(quote, "email");
                              }}
                            >
                              <Mail className="mr-2 h-4 w-4" />
                              Email to Customer
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={async (e) => {
                                // Stop event propagation to prevent the row click handler from firing
                                e.stopPropagation();
                                try {
                                  // Show loading toast
                                  toast({
                                    title: "Processing",
                                    description: "Checking for existing invoices...",
                                  });

                                  // First, check if an invoice already exists for this quote
                                  const allInvoices = await invoiceService.getInvoices();
                                  const existingInvoice = allInvoices.find(inv => inv.quoteId === quote.id);

                                  if (existingInvoice) {
                                    // An invoice already exists for this quote
                                    toast({
                                      title: "Invoice Already Exists",
                                      description: `This quote has already been converted to invoice #${existingInvoice.id.substring(0, 8).toUpperCase()}.`,
                                      variant: "destructive",
                                    });

                                    // If the existing invoice is in draft status, open it for editing
                                    // Otherwise, just navigate to the invoices page
                                    if (existingInvoice.status === 'draft') {
                                      window.location.replace(`/admin/invoices?edit=${existingInvoice.id}`);
                                    } else {
                                      // Navigate to the invoices page and highlight the existing invoice
                                      window.location.replace(`/admin/invoices?highlight=${existingInvoice.id}`);
                                    }
                                    return;
                                  }

                                  // Get the customer for this quote
                                  const customer = customers.find(c => c.id === quote.customerId);
                                  if (!customer) {
                                    toast({
                                      title: "Error",
                                      description: "Customer information not found.",
                                      variant: "destructive",
                                    });
                                    return;
                                  }

                                  // Create a new invoice based on the quote
                                  // Import the Invoice type to ensure type safety
                                  const invoiceData: Omit<Invoice, "id" | "createdAt" | "updatedAt" | "amount"> = {
                                    title: `Invoice for ${quote.title}`,
                                    customerId: quote.customerId,
                                    status: 'draft' as Invoice['status'],
                                    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
                                    quoteId: quote.id, // Link to the original quote
                                    projectId: quote.projectId || undefined,
                                    items: quote.items.map(item => ({
                                      description: item.description,
                                      quantity: item.quantity,
                                      unitPrice: item.unitPrice,
                                      total: item.quantity * item.unitPrice
                                    })),
                                    parentInvoiceId: undefined
                                  };

                                  // Create the invoice
                                  const newInvoice = await invoiceService.createInvoice(invoiceData);

                                  // Success toast
                                  toast({
                                    title: "Invoice Created",
                                    description: "A new invoice has been created from this quote.",
                                  });

                                  // Navigate directly to the invoices page with edit parameter
                                  // Use window.location.replace to prevent back button from returning to this action
                                  window.location.replace(`/admin/invoices?edit=${newInvoice.id}`);
                                } catch (error) {
                                  console.error("Error converting quote to invoice:", error);
                                  toast({
                                    title: "Error",
                                    description: "Failed to convert quote to invoice. Please try again.",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Receipt className="mr-2 h-4 w-4" />
                              Convert to Invoice
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Quote Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        try {
          setIsAddDialogOpen(open);
          if (!open) {
            // Reset form when dialog is closed
            form.reset();
            setSelectedQuote(null);
          }
        } catch (error) {
          console.error("Error toggling dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">
              {selectedQuote ? "Edit Quote" : "New Quote"}
            </DialogTitle>
            <DialogDescription>
              {selectedQuote
                ? "Update quote information"
                : "Enter the details of the new quote"}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddQuote)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quote Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter quote title" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map(customer => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dealId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Related Deal (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select deal" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {deals
                            .filter(deal => form.getValues("customerId") ? deal.customerId === form.getValues("customerId") : true)
                            .map(deal => (
                              <SelectItem key={deal.id} value={deal.id}>
                                {deal.title}
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="projectId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Related Project (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {projects
                            .filter(project => form.getValues("customerId") ? project.customerId === form.getValues("customerId") : true)
                            .map(project => (
                              <SelectItem key={project.id} value={project.id}>
                                {project.title}
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="sent">Sent</SelectItem>
                          <SelectItem value="accepted">Accepted</SelectItem>
                          <SelectItem value="rejected">Rejected</SelectItem>
                          <SelectItem value="expired">Expired</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="validUntil"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valid Until</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Quote Items</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addQuoteItem}
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Add Item
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40%]">Description</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit Price</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {form.watch("items").map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.description`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder="Item description"
                                      className="border-0 p-0 h-8 focus-visible:ring-0"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.quantity`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      min="1"
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-16"
                                      onChange={(e) => {
                                        field.onChange(e);
                                        updateItemTotal(index);
                                      }}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.unitPrice`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      min="0"
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-24"
                                      onChange={(e) => {
                                        field.onChange(e);
                                        updateItemTotal(index);
                                      }}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              control={form.control}
                              name={`items.${index}.total`}
                              render={({ field }) => (
                                <FormItem className="m-0">
                                  <FormControl>
                                    <Input
                                      {...field}
                                      type="number"
                                      disabled
                                      className="border-0 p-0 h-8 focus-visible:ring-0 w-24 bg-transparent"
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => removeQuoteItem(index)}
                              disabled={form.watch("items").length <= 1}
                            >
                              <Trash2 className="h-4 w-4 text-gray-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end pt-2">
                  <div className="w-[200px] space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(calculateQuoteTotal(form.watch("items")))}</span>
                    </div>
                    <div className="flex justify-between text-sm font-bold">
                      <span>Total:</span>
                      <span>{formatCurrency(calculateQuoteTotal(form.watch("items")))}</span>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <div className="flex flex-row gap-2 w-full justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="outline"
                  >
                    {selectedQuote ? "Update & Close" : "Save & Close"}
                  </Button>
                  <Button
                    type="button"
                    className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                    onClick={async () => {
                      try {
                        const formData = form.getValues();

                        // Validate required fields
                        if (!formData.customerId) {
                          toast({
                            title: "Missing Information",
                            description: "Please select a customer before generating.",
                            variant: "destructive",
                          });
                          return;
                        }

                        if (formData.items.length === 0 || !formData.items[0].description) {
                          toast({
                            title: "Missing Information",
                            description: "Please add at least one item before generating.",
                            variant: "destructive",
                          });
                          return;
                        }

                        const quoteData = {
                          title: formData.title,
                          customerId: formData.customerId,
                          status: formData.status,
                          validUntil: formData.validUntil,
                          dealId: formData.dealId === 'none' ? null : formData.dealId,
                          projectId: formData.projectId === 'none' ? null : formData.projectId,
                          items: formData.items.map(item => ({
                            description: item.description,
                            quantity: item.quantity,
                            unitPrice: item.unitPrice,
                            total: item.quantity * item.unitPrice
                          }))
                        };

                        let quoteToPreview: Quote;

                        if (selectedQuote) {
                          // Update existing quote
                          quoteToPreview = await quoteService.updateQuote(selectedQuote.id, quoteData);
                          setSelectedQuote(quoteToPreview);

                          toast({
                            title: "Changes Saved",
                            description: "Quote has been updated and will be previewed.",
                          });
                        } else {
                          // Create new quote
                          quoteToPreview = await quoteService.createQuote(quoteData);
                          setSelectedQuote(quoteToPreview);

                          toast({
                            title: "Quote Created",
                            description: "New quote has been created and will be previewed.",
                          });
                        }

                        // Refresh quotes list
                        const updatedQuotes = await quoteService.getQuotes();
                        setQuotes(updatedQuotes);

                        // Get the customer and open the preview dialog
                        const customer = customers.find(c => c.id === quoteToPreview.customerId);
                        if (customer) {
                          setSelectedCustomer(customer);
                          setIsPreviewDialogOpen(true);
                          setIsAddDialogOpen(false); // Close the edit dialog
                        }
                      } catch (error) {
                        console.error("Error processing quote:", error);
                        toast({
                          title: "Error",
                          description: "Failed to process quote. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Generate
                  </Button>
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => {
        try {
          setIsDeleteDialogOpen(open);
          if (!open) {
            // Reset selected quote when dialog is closed
            setSelectedQuote(null);
          }
        } catch (error) {
          console.error("Error toggling delete dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedQuote?.title}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Quote Preview Dialog */}
      {selectedQuote && selectedCustomer && (
        <QuotePreviewDialog
          open={isPreviewDialogOpen}
          onOpenChange={setIsPreviewDialogOpen}
          quote={selectedQuote}
          customer={selectedCustomer}
          initialTab={activePreviewTab}
          onQuoteUpdated={() => {
            // Refresh quotes list when a quote is updated (e.g., status changed to sent)
            quoteService.getQuotes().then(updatedQuotes => {
              setQuotes(updatedQuotes);
            });
          }}
        />
      )}

      {/* Revise Quote Confirmation Dialog */}
      <Dialog open={isReviseDialogOpen} onOpenChange={setIsReviseDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Revise Quote
            </DialogTitle>
            <DialogDescription>
              <p className="mb-2">You are about to create a revised version of this quote.</p>
              <p className="mb-2">The original quote will be <strong>archived</strong> and a new draft quote will be created.</p>
              <p>This ensures that quotes remain immutable once they've been sent to customers.</p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsReviseDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmReviseQuote}
            >
              Create Revised Quote
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CrmLayout>
  );
};

export default Quotes;
