import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  User,
  Calendar,
  FileText,
  Receipt
} from "lucide-react";
import CrmLayout from "./CrmLayout";
import { customerService, dealService, projectService } from "@/services";
import { Project, Customer, Deal } from "@/types/crm";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";

const projectFormSchema = z.object({
  title: z.string().min(1, "Project title is required"),
  customerId: z.string().min(1, "Customer is required"),
  dealId: z.string().optional(),
  description: z.string().min(1, "Project description is required"),
  status: z.enum(["planning", "in-progress", "review", "completed", "on-hold"]),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  budget: z.coerce.number().optional().or(z.literal('')),
  currentWebsite: z.string().optional(),
  goals: z.string().min(1, "Project goals are required"),
});

type ProjectFormValues = z.infer<typeof projectFormSchema>;

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch data in parallel
        const [projectsData, customersData, dealsData] = await Promise.all([
          projectService.getProjects(),
          customerService.getCustomers(),
          dealService.getDeals()
        ]);

        setProjects(projectsData);
        setCustomers(customersData);
        setDeals(dealsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load projects data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      title: "",
      customerId: "",
      dealId: "none",
      description: "",
      status: "planning",
      startDate: "",
      endDate: "",
      budget: undefined,
      currentWebsite: "",
      goals: "",
    }
  });

  const filteredProjects = projects.filter(project => {
    const matchesSearch =
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || project.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleAddProject = async (data: ProjectFormValues) => {
    console.log("Form data:", data);

    // Prepare the data for submission
    const projectData = {
      ...data,
      // Convert empty strings to undefined for optional fields
      startDate: data.startDate || undefined,
      endDate: data.endDate || undefined,
      budget: data.budget === '' ? undefined : data.budget,
      dealId: data.dealId === 'none' ? undefined : data.dealId,
      currentWebsite: data.currentWebsite || undefined,
    };
    try {
      if (selectedProject) {
        // Update existing project
        await projectService.updateProject(selectedProject.id, projectData);

        // Show appropriate toast message
        const hasChangedDeal = projectData.dealId && projectData.dealId !== selectedProject.dealId;
        const hasChangedCustomer = projectData.customerId && projectData.customerId !== selectedProject.customerId;

        if (hasChangedDeal || hasChangedCustomer) {
          toast({
            title: "Project Updated",
            description: `${data.title} has been updated successfully. Related records have also been updated.`,
          });
        } else {
          toast({
            title: "Project Updated",
            description: `${data.title} has been updated successfully.`,
          });
        }
      } else {
        // Create new project
        await projectService.createProject(projectData);

        // Show appropriate toast message based on whether a deal was associated
        if (projectData.dealId && projectData.dealId !== 'none') {
          toast({
            title: "Project Added",
            description: `${data.title} has been added successfully. The associated deal has been marked as won and the customer is now active.`,
          });
        } else {
          toast({
            title: "Project Added",
            description: `${data.title} has been added successfully. The customer is now marked as active.`,
          });
        }
      }

      // Refresh project list
      const updatedProjects = await projectService.getProjects();
      setProjects(updatedProjects);

      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error saving project:", error);
      toast({
        title: "Error",
        description: "Failed to save project. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    form.reset({
      title: project.title,
      customerId: project.customerId,
      dealId: project.dealId || "none",
      description: project.description,
      status: project.status,
      startDate: project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : "",
      endDate: project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : "",
      budget: project.budget || undefined,
      currentWebsite: project.currentWebsite || "",
      goals: project.goals,
    });
    setIsAddDialogOpen(true);
  };

  const handleDeleteProject = (project: Project) => {
    setSelectedProject(project);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedProject) {
      try {
        await projectService.deleteProject(selectedProject.id);
        toast({
          title: "Project Deleted",
          description: `${selectedProject.title} has been deleted.`,
        });

        // Refresh project list
        const updatedProjects = await projectService.getProjects();
        setProjects(updatedProjects);
      } catch (error) {
        console.error("Error deleting project:", error);
        toast({
          title: "Error",
          description: "Failed to delete project. Please try again.",
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
    setSelectedProject(null);
  };

  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  // Function to navigate to customer edit page
  const handleViewCustomer = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
      // Navigate to the customer page and open the edit modal
      window.location.href = `/admin/customers?edit=${customer.id}`;
    }
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-amber-100 text-amber-800';
      case 'review':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'on-hold':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatusTitle = (status: string) => {
    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  return (
    <CrmLayout
      title="Projects"
      breadcrumbs={[{ title: "Projects" }]}
    >
      <Card className="shadow-sm border-0">
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle className="font-dm-serif text-techlocal-dark">Project Management</CardTitle>
              <CardDescription>Manage your client projects</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder="Search projects..."
                  className="pl-8 w-full sm:w-[250px]"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="review">Review</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="on-hold">On Hold</SelectItem>
                </SelectContent>
              </Select>
              <Button
                onClick={() => {
                  form.reset({
                    title: "",
                    customerId: "",
                    dealId: "none",
                    description: "",
                    status: "planning",
                    startDate: "",
                    endDate: "",
                    budget: undefined,
                    currentWebsite: "",
                    goals: "",
                  });
                  setSelectedProject(null);
                  setIsAddDialogOpen(true);
                }}
                className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Project
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Project Name</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Timeline</TableHead>
                  <TableHead>Budget</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      Loading projects...
                    </TableCell>
                  </TableRow>
                ) : filteredProjects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No projects found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProjects.map((project) => (
                    <TableRow key={project.id}>
                      <TableCell className="font-medium">
                        <button
                          onClick={() => handleEditProject(project)}
                          className="text-left hover:text-techlocal-dark hover:underline"
                        >
                          {project.title}
                        </button>
                      </TableCell>
                      <TableCell>
                        <button
                          onClick={() => handleViewCustomer(project.customerId)}
                          className="text-left hover:text-techlocal-dark hover:underline"
                        >
                          {getCustomerName(project.customerId)}
                        </button>
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                          {formatStatusTitle(project.status)}
                        </span>
                      </TableCell>
                      <TableCell>
                        {project.startDate && project.endDate ? (
                          <span className="text-sm">
                            {new Date(project.startDate).toLocaleDateString()} - {new Date(project.endDate).toLocaleDateString()}
                          </span>
                        ) : project.startDate ? (
                          <span className="text-sm">
                            Started: {new Date(project.startDate).toLocaleDateString()}
                          </span>
                        ) : (
                          <span className="text-sm text-gray-500">Not scheduled</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {project.budget ? (
                          formatCurrency(project.budget)
                        ) : (
                          <span className="text-sm text-gray-500">Not set</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleEditProject(project)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteProject(project)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleViewCustomer(project.customerId)}
                            >
                              <User className="mr-2 h-4 w-4" />
                              View Customer
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link to={`/admin/projects/${project.id}/quotes`}>
                                <FileText className="mr-2 h-4 w-4" />
                                View Quotes
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link to={`/admin/projects/${project.id}/invoices`}>
                                <Receipt className="mr-2 h-4 w-4" />
                                View Invoices
                              </Link>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Project Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
        try {
          setIsAddDialogOpen(open);
          if (!open) {
            // Reset form when dialog is closed
            form.reset();
            setSelectedProject(null);
          }
        } catch (error) {
          console.error("Error toggling dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">
              {selectedProject ? "Edit Project" : "Add New Project"}
            </DialogTitle>
            <DialogDescription>
              {selectedProject
                ? "Update project information"
                : "Enter the details of the new project"}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={(e) => {
                try {
                  form.handleSubmit(handleAddProject)(e);
                } catch (error) {
                  console.error("Error submitting form:", error);
                  toast({
                    title: "Error",
                    description: "There was an issue submitting the form. Please try again.",
                    variant: "destructive",
                  });
                }
              }} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter project title" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map(customer => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="dealId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Related Deal (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select deal" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {deals
                            .filter(deal => form.getValues("customerId") ? deal.customerId === form.getValues("customerId") : true)
                            .map(deal => (
                              <SelectItem key={deal.id} value={deal.id}>
                                {deal.title}
                              </SelectItem>
                            ))
                          }
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="planning">Planning</SelectItem>
                          <SelectItem value="in-progress">In Progress</SelectItem>
                          <SelectItem value="review">Review</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="on-hold">On Hold</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget (ZAR)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          placeholder="Enter project budget"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="currentWebsite"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Website (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter current website URL" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Description</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter project description"
                        className="min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Goals</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter project goals"
                        className="min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                >
                  {selectedProject ? "Update Project" : "Add Project"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => {
        try {
          setIsDeleteDialogOpen(open);
          if (!open) {
            // Reset selected project when dialog is closed
            setSelectedProject(null);
          }
        } catch (error) {
          console.error("Error toggling delete dialog:", error);
          toast({
            title: "Error",
            description: "There was an issue with the dialog. Please try again.",
            variant: "destructive",
          });
        }
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedProject?.title}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CrmLayout>
  );
};

export default Projects;
