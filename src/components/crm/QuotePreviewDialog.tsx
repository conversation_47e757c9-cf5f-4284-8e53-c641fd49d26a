import React from "react";
import { Quote, Customer } from "@/types/crm";
import { quoteUtilsService } from "@/services/quoteUtilsService";
import { quoteService } from "@/services";
import QuotePreview from "./QuotePreview";
import DocumentPreviewDialog from "./DocumentPreviewDialog";

interface QuotePreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  quote: Quote;
  customer: Customer;
  onQuoteUpdated?: () => void;
  initialTab?: "preview" | "download" | "email";
}

const QuotePreviewDialog: React.FC<QuotePreviewDialogProps> = (props) => {
  const { quote, onQuoteUpdated } = props;

  return (
    <DocumentPreviewDialog
      {...props}
      document={quote}
      documentType="quote"
      onDocumentUpdated={onQuoteUpdated}
      getCompanyInfo={quoteUtilsService.getCompanyInfo}
      generatePDF={(element, document, customer) =>
        quoteUtilsService.generatePDF(element, document, customer)
      }
      downloadPDF={(pdfBase64, documentId) =>
        quoteUtilsService.downloadQuotePDF(pdfBase64, documentId)
      }
      sendEmail={(document, customer, pdfBase64, message) =>
        quoteUtilsService.sendQuoteEmail(document, customer, pdfBase64, message)
      }
      updateStatus={(documentId, status) => {
        return quoteService.updateQuote(documentId, { status: status as "draft" | "sent" | "accepted" | "rejected" | "expired" | "archived" })
          .then(() => {}); // Convert Promise<Quote> to Promise<void>
      }}
      PreviewComponent={QuotePreview}
    />
  );
};

export default QuotePreviewDialog;
