import { ReactNode, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { 
  LayoutDashboard, 
  Users, 
  LineChart, 
  FolderKanban, 
  FileText, 
  Receipt, 
  Settings, 
  Menu, 
  X, 
  LogOut 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface CrmLayoutProps {
  children: ReactNode;
  title: string;
  breadcrumbs?: {
    title: string;
    href?: string;
  }[];
}

const CrmLayout = ({ children, title, breadcrumbs = [] }: CrmLayoutProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const location = useLocation();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      toast({
        title: "Signed out",
        description: "You have been signed out successfully",
      });
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const navItems = [
    { title: "Dashboard", icon: <LayoutDashboard size={20} />, href: "/admin/dashboard" },
    { title: "Customers", icon: <Users size={20} />, href: "/admin/customers" },
    { title: "Sales Pipeline", icon: <LineChart size={20} />, href: "/admin/sales" },
    { title: "Projects", icon: <FolderKanban size={20} />, href: "/admin/projects" },
    { title: "Quotes", icon: <FileText size={20} />, href: "/admin/quotes" },
    { title: "Invoices", icon: <Receipt size={20} />, href: "/admin/invoices" },
    { title: "Settings", icon: <Settings size={20} />, href: "/admin/settings" },
  ];

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside 
        className={`fixed inset-y-0 left-0 z-30 w-64 transform bg-white border-r border-gray-200 transition-transform duration-300 ease-in-out md:translate-x-0 ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <Link to="/admin/dashboard" className="flex items-center">
              <span className="text-xl font-bold font-dm-serif text-techlocal-dark">Tech Local Admin</span>
            </Link>
            <button 
              className="md:hidden"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X size={20} />
            </button>
          </div>
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navItems.map((item) => (
              <Link
                key={item.href}
                to={item.href}
                className={`flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                  isActive(item.href)
                    ? "bg-techlocal-beige text-techlocal-dark"
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.title}
              </Link>
            ))}
          </nav>
          <div className="p-4 border-t border-gray-200">
            <Button 
              variant="outline" 
              onClick={handleLogout} 
              className="flex w-full items-center justify-center gap-2 text-techlocal-dark border-techlocal-dark hover:bg-techlocal-beige/50"
            >
              <LogOut size={16} />
              Logout
            </Button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex flex-col flex-1 md:ml-64">
        {/* Header */}
        <header className="sticky top-0 z-10 flex items-center h-16 bg-white border-b border-gray-200 px-4 md:px-6">
          <button 
            className="mr-4 md:hidden"
            onClick={() => setIsSidebarOpen(true)}
          >
            <Menu size={24} />
          </button>
          
          <div className="flex-1">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin/dashboard">Admin</BreadcrumbLink>
                </BreadcrumbItem>
                {breadcrumbs.map((crumb, index) => (
                  <BreadcrumbItem key={index}>
                    <BreadcrumbSeparator />
                    {crumb.href ? (
                      <BreadcrumbLink href={crumb.href}>{crumb.title}</BreadcrumbLink>
                    ) : (
                      <BreadcrumbPage>{crumb.title}</BreadcrumbPage>
                    )}
                  </BreadcrumbItem>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          
          <div>
            <Link to="/" className="text-sm text-gray-600 hover:text-gray-900">
              Back to Site
            </Link>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 p-4 md:p-6 overflow-y-auto">
          <div className="mb-6">
            <h1 className="text-2xl md:text-3xl font-dm-serif font-bold text-techlocal-dark">{title}</h1>
          </div>
          {children}
        </main>
      </div>
    </div>
  );
};

export default CrmLayout;
