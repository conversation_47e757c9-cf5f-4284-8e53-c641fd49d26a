import { StyleSheet } from '@react-pdf/renderer';
import { Text } from '@react-pdf/renderer';

// Colors from Tailwind config
// Note: We're using hardcoded values because importing the Tailwind config directly
// can cause issues with the PDF renderer and build process
export const colors = {
  gray600: '#4b5563',
  gray100: '#f3f4f6',
  gray200: '#e5e7eb',
  brand: '#403E43', // techlocal.dark from the Tailwind config
};

/* column widths as percentages of the content width (must add up to 100) */
export const COLS = [55, 15, 15, 15];

/* ----------------------------- styles ----------------------------------- */
export const styles = StyleSheet.create({
  page: { 
    padding: 36, 
    fontSize: 10, 
    fontFamily: 'Helvetica', 
    lineHeight: 1.4 
  },

  /* layout helpers */
  row: { 
    flexDirection: 'row', 
    justifyContent: 'space-between' 
  },
  right: { 
    textAlign: 'right' 
  },
  companyInfo: { 
    alignItems: 'flex-end', 
    textAlign: 'right' 
  },

  /* typographic */
  h1: { 
    fontSize: 22, 
    fontWeight: 'bold', 
    color: colors.brand, 
    marginBottom: 10 
  },
  h2: { 
    fontSize: 12, 
    fontWeight: 'bold', 
    color: colors.gray600, 
    marginBottom: 4 
  },
  bold: { 
    fontWeight: 'bold' 
  },

  /* table */
  tableHead: { 
    flexDirection: 'row', 
    backgroundColor: colors.gray100, 
    borderTop: `1pt solid ${colors.gray200}`, 
    borderBottom: `1pt solid ${colors.gray200}` 
  },
  th: { 
    paddingVertical: 6, 
    paddingHorizontal: 4, 
    fontWeight: 'bold', 
    borderRight: `1pt solid ${colors.gray200}` 
  },
  td: { 
    paddingVertical: 6, 
    paddingHorizontal: 4, 
    borderRight: `1pt solid ${colors.gray200}`, 
    borderBottom: `1pt solid ${colors.gray200}` 
  },

  /* misc */
  notes: { 
    marginTop: 20, 
    color: colors.gray600 
  },
  footer: { 
    marginTop: 36, 
    textAlign: 'center', 
    fontStyle: 'italic', 
    color: colors.gray600 
  },
  
  /* totals */
  totals: {
    marginTop: 12,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 2,
  },
  totalLabel: {
    marginRight: 6,
  },
  totalValue: {
    width: 70,
    textAlign: 'right',
  },
});

/* helper to render a table cell with consistent width */
export const Cell: React.FC<React.PropsWithChildren<{ idx: number; align?: 'left' | 'right' }>> = 
  ({ idx, align = 'left', children }) => (
    <Text
      style={{
        ...(styles.td as any),
        width: `${COLS[idx]}%`,
        textAlign: align,
        borderRight: idx === COLS.length - 1 ? 'none' : `1pt solid ${colors.gray200}`,
      }}
    >
      {children}
    </Text>
  );

export const HeaderCell: React.FC<React.PropsWithChildren<{ idx: number; align?: 'left' | 'right' }>> = 
  ({ idx, align = 'left', children }) => (
    <Text
      style={{
        ...(styles.th as any),
        width: `${COLS[idx]}%`,
        textAlign: align,
        borderRight: idx === COLS.length - 1 ? 'none' : `1pt solid ${colors.gray200}`,
      }}
    >
      {children}
    </Text>
  );
