import React from "react";
import { Invoice, Customer } from "@/types/crm";
import { invoiceUtilsService } from "@/services/invoiceUtilsService";
import { invoiceService } from "@/services";
import InvoicePreview from "./InvoicePreview";
import DocumentPreviewDialog from "./DocumentPreviewDialog";

interface InvoicePreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: Invoice;
  customer: Customer;
  onInvoiceUpdated?: () => void;
  initialTab?: "preview" | "download" | "email";
}

const InvoicePreviewDialog: React.FC<InvoicePreviewDialogProps> = (props) => {
  const { invoice, onInvoiceUpdated } = props;

  return (
    <DocumentPreviewDialog
      {...props}
      document={invoice}
      documentType="invoice"
      onDocumentUpdated={onInvoiceUpdated}
      getCompanyInfo={invoiceUtilsService.getCompanyInfo}
      generatePDF={(element, document, customer) =>
        invoiceUtilsService.generatePDF(element, document, customer)
      }
      downloadPDF={(pdfBase64, documentId, document) =>
        invoiceUtilsService.downloadInvoicePDF(pdfBase64, documentId, document)
      }
      sendEmail={(document, customer, pdfBase64, message) =>
        invoiceUtilsService.sendInvoiceEmail(document, customer, pdfBase64, message)
      }
      updateStatus={async (documentId, status) => {
        await invoiceService.updateInvoice(documentId, {
          status: status as "draft" | "sent" | "paid" | "overdue" | "cancelled" | "archived"
        });
      }}
      PreviewComponent={InvoicePreview}
    />
  );
};

export default InvoicePreviewDialog;
