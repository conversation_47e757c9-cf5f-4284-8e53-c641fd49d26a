import { useState, useEffect, useRef } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import {
  Card,
  CardContent
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  User,
  Percent
} from "lucide-react";
import CrmLayout from "./CrmLayout";
import { customerService, dealService, adminUserService } from "@/services";
import { AdminUser } from "@/services/adminUserService";
import { Deal, Customer } from "@/types/crm";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency } from "@/lib/utils";

// Define item types for drag and drop
const ItemTypes = {
  DEAL: 'deal'
};

interface DraggableDealCardProps {
  deal: Deal;
  getCustomerName: (customerId: string) => string;
  getAssignedUserName: (userId?: string) => string;
  handleEditDeal: (deal: Deal) => void;
  handleDeleteDeal: (deal: Deal) => void;
  handleMoveDeal: (dealId: string, newStage: Deal['stage']) => void;
}

const DraggableDealCard = ({
  deal,
  getCustomerName,
  getAssignedUserName,
  handleEditDeal,
  handleDeleteDeal,
  handleMoveDeal
}: DraggableDealCardProps) => {
  const ref = useRef<HTMLDivElement>(null);

  // Set up drag source
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.DEAL,
    item: { id: deal.id, currentStage: deal.stage },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }));

  // Apply the drag ref to our element
  drag(ref);

  return (
    <div
      ref={ref}
      className={cn(
        "cursor-move",
        isDragging ? "opacity-50" : "opacity-100"
      )}
    >
      <Card className="shadow-sm border-0 hover:shadow-md transition-shadow">
        <CardContent className="p-3">
          <div className="flex justify-between items-start">
            <button
              className="font-medium text-sm hover:text-techlocal-dark hover:underline line-clamp-2 text-left"
              onClick={(e) => {
                if (isDragging) {
                  e.preventDefault();
                  return;
                }
                handleEditDeal(deal);
              }}
            >
              {deal.title}
            </button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => handleEditDeal(deal)}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleDeleteDeal(deal)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>

              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="mt-2 space-y-1">
            <div className="flex items-center text-xs text-gray-500">
              <User className="mr-1 h-3 w-3" />
              <span className="truncate">{getCustomerName(deal.customerId)}</span>
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <DollarSign className="mr-1 h-3 w-3" />
              <span>{formatCurrency(deal.value)}</span>
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <Percent className="mr-1 h-3 w-3" />
              <span>{deal.probability}%</span>
            </div>
            {deal.expectedCloseDate && (
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="mr-1 h-3 w-3" />
                <span>{new Date(deal.expectedCloseDate).toLocaleDateString()}</span>
              </div>
            )}
            <div className="flex items-center text-xs">
              <User className="mr-1 h-3 w-3 text-gray-500" />
              <span className={deal.assignedTo && deal.assignedTo !== 'unassigned' ? 'text-blue-600 font-medium' : 'text-gray-500'}>
                {getAssignedUserName(deal.assignedTo)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

interface DroppableColumnProps {
  stage: Deal['stage'];
  deals: Deal[];
  getCustomerName: (customerId: string) => string;
  getAssignedUserName: (userId?: string) => string;
  handleEditDeal: (deal: Deal) => void;
  handleDeleteDeal: (deal: Deal) => void;
  handleMoveDeal: (dealId: string, newStage: Deal['stage']) => void;
  getStageColor: (stage: Deal['stage']) => string;
  formatStageTitle: (stage: string) => string;
  stageValue: number;
}

const DroppableColumn = ({
  stage,
  deals,
  getCustomerName,
  getAssignedUserName,
  handleEditDeal,
  handleDeleteDeal,
  handleMoveDeal,
  getStageColor,
  formatStageTitle,
  stageValue
}: DroppableColumnProps) => {
  const ref = useRef<HTMLDivElement>(null);

  // Set up drop target
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.DEAL,
    drop: (item: { id: string, currentStage: Deal['stage'] }) => {
      if (item.currentStage !== stage) {
        handleMoveDeal(item.id, stage);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  }));

  // Apply the drop ref to our element
  drop(ref);

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStageColor(stage)}`}>
            {formatStageTitle(stage)}
          </span>
          <span className="ml-2 text-sm text-gray-500">{deals.length}</span>
        </div>
        <span className="text-sm font-medium">{formatCurrency(stageValue)}</span>
      </div>

      <div
        ref={ref}
        className={cn(
          "flex-1 space-y-3 p-2 rounded-md min-h-[200px]",
          isOver ? "bg-gray-100" : "bg-transparent"
        )}
      >
        {deals.map(deal => (
          <DraggableDealCard
            key={deal.id}
            deal={deal}
            getCustomerName={getCustomerName}
            getAssignedUserName={getAssignedUserName}
            handleEditDeal={handleEditDeal}
            handleDeleteDeal={handleDeleteDeal}
            handleMoveDeal={handleMoveDeal}
          />
        ))}

        {deals.length === 0 && (
          <div className="bg-gray-50 rounded-md p-4 text-center text-sm text-gray-500">
            No deals in this stage
          </div>
        )}
      </div>
    </div>
  );
};

const dealFormSchema = z.object({
  title: z.string().min(1, "Deal title is required"),
  customerId: z.string().min(1, "Customer is required"),
  value: z.coerce.number().min(0, "Deal value is required"),
  stage: z.enum(["lead", "qualified", "proposal", "negotiation", "closed-won", "closed-lost"]),
  probability: z.coerce.number().min(0).max(100, "Probability must be between 0 and 100"),
  expectedCloseDate: z.string().optional(),
  notes: z.string().optional(),
  assignedTo: z.string().optional().default("unassigned"),
});

type DealFormValues = z.infer<typeof dealFormSchema>;

const SalesPipeline = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [stageValues, setStageValues] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch deals, customers, and admin users in parallel
        const [dealsData, customersData, adminUsersData] = await Promise.all([
          dealService.getDeals(),
          customerService.getCustomers(),
          adminUserService.getAdminUsers()
        ]);

        setDeals(dealsData);
        setCustomers(customersData);
        setAdminUsers(adminUsersData);

        // Calculate stage values
        const stages = ['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost'];
        const values: Record<string, number> = {};

        for (const stage of stages) {
          values[stage] = await dealService.getTotalDealValueByStage(stage as Deal['stage']);
        }

        setStageValues(values);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load sales pipeline data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const form = useForm<DealFormValues>({
    resolver: zodResolver(dealFormSchema),
    defaultValues: {
      title: "",
      customerId: "",
      value: 0,
      stage: "lead",
      probability: 10,
      expectedCloseDate: "",
      notes: "",
      assignedTo: "",
    }
  });

  const handleAddDeal = async (data: DealFormValues) => {
    // Check if we're updating a deal to closed-won
    const isMarkingAsWon = selectedDeal &&
                          selectedDeal.stage !== 'closed-won' &&
                          data.stage === 'closed-won';
    try {
      // Ensure all required fields are present
      const dealData = {
        title: data.title,
        customerId: data.customerId,
        value: data.value,
        stage: data.stage,
        probability: data.probability,
        expectedCloseDate: data.expectedCloseDate || undefined,
        notes: data.notes || undefined,
        assignedTo: data.assignedTo === 'unassigned' ? null : data.assignedTo,
      };

      if (selectedDeal) {
        // Update existing deal
        await dealService.updateDeal(selectedDeal.id, dealData);
        if (isMarkingAsWon) {
          toast({
            title: "Deal Won & Project Created",
            description: `${data.title} has been marked as won and a new project has been created.`,
          });
        } else {
          toast({
            title: "Deal Updated",
            description: `${data.title} has been updated successfully.`,
          });
        }
      } else {
        // Create new deal
        await dealService.createDeal(dealData);
        toast({
          title: "Deal Added",
          description: `${data.title} has been added successfully.`,
        });
      }

      // Refresh data
      const [updatedDeals, updatedStageValues] = await Promise.all([
        dealService.getDeals(),
        Promise.all(['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost']
          .map(async (stage) => {
            const value = await dealService.getTotalDealValueByStage(stage as Deal['stage']);
            return { stage, value };
          }))
      ]);

      setDeals(updatedDeals);

      const newStageValues: Record<string, number> = {};
      updatedStageValues.forEach(({ stage, value }) => {
        newStageValues[stage] = value;
      });
      setStageValues(newStageValues);

      setIsAddDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error saving deal:", error);
      toast({
        title: "Error",
        description: "Failed to save deal. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    form.reset({
      title: deal.title,
      customerId: deal.customerId,
      value: deal.value,
      stage: deal.stage,
      probability: deal.probability,
      expectedCloseDate: deal.expectedCloseDate ? new Date(deal.expectedCloseDate).toISOString().split('T')[0] : "",
      notes: deal.notes || "",
      assignedTo: deal.assignedTo || "unassigned",
    });
    setIsAddDialogOpen(true);
  };

  const handleDeleteDeal = (deal: Deal) => {
    setSelectedDeal(deal);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedDeal) {
      try {
        await dealService.deleteDeal(selectedDeal.id);
        toast({
          title: "Deal Deleted",
          description: `${selectedDeal.title} has been deleted.`,
        });

        // Refresh data
        const [updatedDeals, updatedStageValues] = await Promise.all([
          dealService.getDeals(),
          Promise.all(['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost']
            .map(async (stage) => {
              const value = await dealService.getTotalDealValueByStage(stage as Deal['stage']);
              return { stage, value };
            }))
        ]);

        setDeals(updatedDeals);

        const newStageValues: Record<string, number> = {};
        updatedStageValues.forEach(({ stage, value }) => {
          newStageValues[stage] = value;
        });
        setStageValues(newStageValues);
      } catch (error) {
        console.error("Error deleting deal:", error);
        toast({
          title: "Error",
          description: "Failed to delete deal. Please try again.",
          variant: "destructive",
        });
      }
    }
    setIsDeleteDialogOpen(false);
    setSelectedDeal(null);
  };

  // Group deals by stage
  const dealsByStage = {
    lead: deals.filter(deal => deal.stage === 'lead'),
    qualified: deals.filter(deal => deal.stage === 'qualified'),
    proposal: deals.filter(deal => deal.stage === 'proposal'),
    negotiation: deals.filter(deal => deal.stage === 'negotiation'),
    'closed-won': deals.filter(deal => deal.stage === 'closed-won'),
    'closed-lost': deals.filter(deal => deal.stage === 'closed-lost'),
  };

  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  const getAssignedUserName = (userId?: string) => {
    if (!userId || userId === 'unassigned') return 'Unassigned';
    const user = adminUsers.find(u => u.id === userId);

    if (!user) return 'Unknown User';

    // Return the name if available, otherwise show a shortened ID with a prefix
    return user.name || `Admin (${userId.substring(0, 8)}...)`;
  };

  const getStageColor = (stage: Deal['stage']) => {
    switch (stage) {
      case 'lead':
        return 'bg-blue-100 text-blue-800';
      case 'qualified':
        return 'bg-indigo-100 text-indigo-800';
      case 'proposal':
        return 'bg-purple-100 text-purple-800';
      case 'negotiation':
        return 'bg-amber-100 text-amber-800';
      case 'closed-won':
        return 'bg-green-100 text-green-800';
      case 'closed-lost':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStageTitle = (stage: string) => {
    return stage.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Handle moving a deal to a new stage with optimistic updates
  const handleMoveDeal = async (dealId: string, newStage: Deal['stage']) => {
    const dealToMove = deals.find(d => d.id === dealId);
    if (!dealToMove) return;

    // Store original stage for rollback if needed
    const originalStage = dealToMove.stage;

    // Check if we're marking a deal as won
    const isMarkingAsWon = dealToMove.stage !== 'closed-won' && newStage === 'closed-won';

    try {
      // Optimistically update the UI immediately
      const updatedDeals = deals.map(deal =>
        deal.id === dealId ? { ...deal, stage: newStage } : deal
      );
      setDeals(updatedDeals);

      // Calculate new stage values optimistically
      const newStageValues = { ...stageValues };
      // Subtract the deal value from the original stage
      if (newStageValues[originalStage]) {
        newStageValues[originalStage] -= dealToMove.value;
      }
      // Add the deal value to the new stage
      if (newStageValues[newStage]) {
        newStageValues[newStage] += dealToMove.value;
      }
      setStageValues(newStageValues);

      // Now perform the actual update in the background
      await dealService.updateDeal(dealId, { stage: newStage });

      // Show appropriate toast message
      if (isMarkingAsWon) {
        toast({
          title: "Deal Won & Project Created",
          description: `${dealToMove.title} has been marked as won and a new project has been created.`,
        });
      } else {
        toast({
          title: "Deal Moved",
          description: `${dealToMove.title} has been moved to ${formatStageTitle(newStage)}.`,
        });
      }

      // Refresh data in the background to ensure consistency
      const [serverDeals, updatedStageValues] = await Promise.all([
        dealService.getDeals(),
        Promise.all(['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost']
          .map(async (stage) => {
            const value = await dealService.getTotalDealValueByStage(stage as Deal['stage']);
            return { stage, value };
          }))
      ]);

      // Update with server data to ensure consistency
      setDeals(serverDeals);

      const serverStageValues: Record<string, number> = {};
      updatedStageValues.forEach(({ stage, value }) => {
        serverStageValues[stage] = value;
      });
      setStageValues(serverStageValues);
    } catch (error) {
      console.error("Error moving deal:", error);

      // Revert the optimistic update if there was an error
      const revertedDeals = deals.map(deal =>
        deal.id === dealId ? { ...deal, stage: originalStage } : deal
      );
      setDeals(revertedDeals);

      // Refresh stage values to revert the optimistic update
      const [updatedStageValues] = await Promise.all([
        Promise.all(['lead', 'qualified', 'proposal', 'negotiation', 'closed-won', 'closed-lost']
          .map(async (stage) => {
            const value = await dealService.getTotalDealValueByStage(stage as Deal['stage']);
            return { stage, value };
          }))
      ]);

      const revertedStageValues: Record<string, number> = {};
      updatedStageValues.forEach(({ stage, value }) => {
        revertedStageValues[stage] = value;
      });
      setStageValues(revertedStageValues);

      toast({
        title: "Error",
        description: "Failed to move deal. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <CrmLayout
        title="Sales Pipeline"
        breadcrumbs={[{ title: "Sales Pipeline" }]}
      >
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-lg font-medium">Pipeline Overview</h2>
          <p className="text-sm text-gray-500">Drag deals between stages to update their status</p>
        </div>
        <Button
          onClick={() => {
            form.reset({
              title: "",
              customerId: "",
              value: 0,
              stage: "lead",
              probability: 10,
              expectedCloseDate: "",
              notes: "",
              assignedTo: "unassigned",
            });
            setSelectedDeal(null);
            setIsAddDialogOpen(true);
          }}
          className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Deal
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-lg text-gray-500">Loading sales pipeline data...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {Object.entries(dealsByStage).map(([stage, stageDeals]) => (
            <DroppableColumn
              key={stage}
              stage={stage as Deal['stage']}
              deals={stageDeals}
              getCustomerName={getCustomerName}
              getAssignedUserName={getAssignedUserName}
              handleEditDeal={handleEditDeal}
              handleDeleteDeal={handleDeleteDeal}
              handleMoveDeal={handleMoveDeal}
              getStageColor={getStageColor}
              formatStageTitle={formatStageTitle}
              stageValue={stageValues[stage as keyof typeof stageValues]}
            />
          ))}
        </div>
      )}

      {/* Add/Edit Deal Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">
              {selectedDeal ? "Edit Deal" : "Add New Deal"}
            </DialogTitle>
            <DialogDescription>
              {selectedDeal
                ? "Update deal information"
                : "Enter the details of the new deal"}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddDeal)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deal Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter deal title" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map(customer => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="value"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimated Deal Value (ZAR)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          placeholder="Enter deal value"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="stage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stage</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select stage" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="lead">Lead</SelectItem>
                          <SelectItem value="qualified">Qualified</SelectItem>
                          <SelectItem value="proposal">Proposal</SelectItem>
                          <SelectItem value="negotiation">Negotiation</SelectItem>
                          <SelectItem value="closed-won">Closed Won</SelectItem>
                          <SelectItem value="closed-lost">Closed Lost</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="probability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Probability (%)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          max="100"
                          placeholder="Enter probability"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="expectedCloseDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expected Close Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="assignedTo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assigned To</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Unassigned" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="unassigned">Unassigned</SelectItem>
                          {adminUsers.map(user => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.name || `Admin (${user.id.substring(0, 8)}...)`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Enter any additional notes about this deal"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
                >
                  {selectedDeal ? "Update Deal" : "Add Deal"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="font-dm-serif text-techlocal-dark">Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedDeal?.title}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CrmLayout>
    </DndProvider>
  );
};

export default SalesPipeline;
