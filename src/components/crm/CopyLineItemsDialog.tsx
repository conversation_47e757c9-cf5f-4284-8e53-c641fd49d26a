import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogD<PERSON>cription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Copy, Plus, X, Replace } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

export type LineItemAction = "overwrite" | "add" | "keep";

interface CopyLineItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAction: (action: LineItemAction) => void;
  sourceType: "quote" | "invoice";
  sourceName: string;
  hasExistingItems: boolean;
}

const CopyLineItemsDialog: React.FC<CopyLineItemsDialogProps> = ({
  open,
  onOpenChange,
  onAction,
  sourceType,
  sourceName,
  hasExistingItems,
}) => {
  const [selectedAction, setSelectedAction] = React.useState<LineItemAction>(hasExistingItems ? "add" : "overwrite");

  // Reset the selected action when the dialog opens
  React.useEffect(() => {
    if (open) {
      setSelectedAction(hasExistingItems ? "add" : "overwrite");
    }
  }, [open, hasExistingItems]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="font-dm-serif text-techlocal-dark flex items-center gap-2">
            <Copy className="h-5 w-5 text-techlocal-dark" />
            Line Items from {sourceType.charAt(0).toUpperCase() + sourceType.slice(1)}
          </DialogTitle>
          <DialogDescription>
            <p className="mb-4">
              How would you like to handle the line items from {sourceType} "{sourceName}"?
            </p>
          </DialogDescription>
        </DialogHeader>

        <RadioGroup
          value={selectedAction}
          onValueChange={(value) => setSelectedAction(value as LineItemAction)}
          className="space-y-4"
        >
          {hasExistingItems && (
            <div className="flex items-start space-x-3 p-3 border rounded-md hover:bg-gray-50 cursor-pointer">
              <RadioGroupItem value="overwrite" id="overwrite" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="overwrite" className="flex items-center cursor-pointer">
                  <Replace className="h-5 w-5 mr-2 text-blue-600" />
                  <span className="font-medium">Replace existing items</span>
                </Label>
                <p className="text-sm text-gray-500 mt-1 ml-7">
                  Remove all existing line items and replace them with items from the {sourceType}.
                </p>
              </div>
            </div>
          )}

          {hasExistingItems && (
            <div className="flex items-start space-x-3 p-3 border rounded-md hover:bg-gray-50 cursor-pointer">
              <RadioGroupItem value="add" id="add" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="add" className="flex items-center cursor-pointer">
                  <Plus className="h-5 w-5 mr-2 text-green-600" />
                  <span className="font-medium">Add to existing items</span>
                </Label>
                <p className="text-sm text-gray-500 mt-1 ml-7">
                  Keep your existing line items and add the items from the {sourceType} at the end.
                </p>
              </div>
            </div>
          )}

          <div className="flex items-start space-x-3 p-3 border rounded-md hover:bg-gray-50 cursor-pointer">
            <RadioGroupItem value="keep" id="keep" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="keep" className="flex items-center cursor-pointer">
                <X className="h-5 w-5 mr-2 text-red-600" />
                <span className="font-medium">Keep current items only</span>
              </Label>
              <p className="text-sm text-gray-500 mt-1 ml-7">
                Don't copy any line items from the {sourceType}. {hasExistingItems ? "Keep only your existing items." : "Start with an empty list."}
              </p>
            </div>
          </div>
        </RadioGroup>

        <DialogFooter className="mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              onAction(selectedAction);
              onOpenChange(false);
            }}
            className="bg-techlocal-dark hover:bg-techlocal-dark/80 text-white"
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CopyLineItemsDialog;
