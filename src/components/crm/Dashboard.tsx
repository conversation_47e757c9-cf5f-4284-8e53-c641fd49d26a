import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  Users,
  FileText,
  FolderKanban,
  Receipt,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Clock
} from "lucide-react";
import { Link } from "react-router-dom";
import CrmLayout from "./CrmLayout";
import {
  customerService,
  dealService,
  projectService,
  quoteService,
  invoiceService,
  activityService
} from "@/services";
import { Customer, Deal, Project, Quote, Invoice, Activity } from "@/types/crm";
import { formatCurrency } from "@/lib/utils";

const Dashboard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [deals, setDeals] = useState<Deal[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  const [totalPipeline, setTotalPipeline] = useState(0);
  const [weightedPipeline, setWeightedPipeline] = useState(0);
  const [totalInvoiced, setTotalInvoiced] = useState(0);
  const [totalPaid, setTotalPaid] = useState(0);
  const [totalOutstanding, setTotalOutstanding] = useState(0);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Fetch all data in parallel
        const [
          customersData,
          dealsData,
          projectsData,
          quotesData,
          invoicesData,
          activitiesData,
          pipelineValue,
          weightedValue,
          invoicedAmount,
          paidAmount,
          outstandingAmount
        ] = await Promise.all([
          customerService.getCustomers(),
          dealService.getDeals(),
          projectService.getProjects(),
          quoteService.getQuotes(),
          invoiceService.getInvoices(),
          activityService.getRecentActivities(5),
          dealService.getTotalSalesPipelineValue(),
          dealService.getWeightedSalesPipelineValue(),
          invoiceService.getTotalInvoicedAmount(),
          invoiceService.getTotalPaidAmount(),
          invoiceService.getTotalOutstandingAmount()
        ]);

        setCustomers(customersData);
        setDeals(dealsData);
        setProjects(projectsData);
        setQuotes(quotesData);
        setInvoices(invoicesData);
        setRecentActivities(activitiesData);

        setTotalPipeline(pipelineValue);
        setWeightedPipeline(weightedValue);
        setTotalInvoiced(invoicedAmount);
        setTotalPaid(paidAmount);
        setTotalOutstanding(outstandingAmount);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Derived values
  const activeCustomers = customers.filter(c => c.status === 'active').length;
  const leadCustomers = customers.filter(c => c.status === 'lead').length;

  const activeProjects = projects.filter(p => p.status === 'in-progress').length;
  const plannedProjects = projects.filter(p => p.status === 'planning').length;

  const openDeals = deals.filter(d => !['closed-won', 'closed-lost'].includes(d.stage)).length;
  const wonDeals = deals.filter(d => d.stage === 'closed-won').length;

  return (
    <CrmLayout title="Dashboard" breadcrumbs={[]}>
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <p className="text-lg text-gray-500">Loading dashboard data...</p>
        </div>
      ) : (
        <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* Customers Card */}
        <Card className="shadow-sm border-0 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mb-2">
              <Users size={20} className="text-blue-600" />
            </div>
            <CardTitle className="text-lg font-dm-serif">Customers</CardTitle>
            <CardDescription>Customer overview</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">Active</span>
              <span className="text-sm font-medium">{activeCustomers}</span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-500">Leads</span>
              <span className="text-sm font-medium">{leadCustomers}</span>
            </div>
            <Link to="/admin/customers">
              <Button variant="outline" className="w-full text-blue-600 border-blue-200 hover:bg-blue-50">
                View All Customers
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Sales Pipeline Card */}
        <Card className="shadow-sm border-0 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mb-2">
              <TrendingUp size={20} className="text-green-600" />
            </div>
            <CardTitle className="text-lg font-dm-serif">Sales Pipeline</CardTitle>
            <CardDescription>Deal overview</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">Open Deals</span>
              <span className="text-sm font-medium">{openDeals}</span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-500">Won Deals</span>
              <span className="text-sm font-medium">{wonDeals}</span>
            </div>
            <Link to="/admin/sales">
              <Button variant="outline" className="w-full text-green-600 border-green-200 hover:bg-green-50">
                View Sales Pipeline
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Projects Card */}
        <Card className="shadow-sm border-0 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mb-2">
              <FolderKanban size={20} className="text-purple-600" />
            </div>
            <CardTitle className="text-lg font-dm-serif">Projects</CardTitle>
            <CardDescription>Project overview</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">Active</span>
              <span className="text-sm font-medium">{activeProjects}</span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-500">Planning</span>
              <span className="text-sm font-medium">{plannedProjects}</span>
            </div>
            <Link to="/admin/projects">
              <Button variant="outline" className="w-full text-purple-600 border-purple-200 hover:bg-purple-50">
                View All Projects
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Financials Card */}
        <Card className="shadow-sm border-0 hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="p-3 bg-amber-100 rounded-full w-12 h-12 flex items-center justify-center mb-2">
              <Receipt size={20} className="text-amber-600" />
            </div>
            <CardTitle className="text-lg font-dm-serif">Financials</CardTitle>
            <CardDescription>Invoice overview</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">Invoiced</span>
              <span className="text-sm font-medium">{formatCurrency(totalInvoiced)}</span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-500">Outstanding</span>
              <span className="text-sm font-medium">{formatCurrency(totalOutstanding)}</span>
            </div>
            <Link to="/admin/invoices">
              <Button variant="outline" className="w-full text-amber-600 border-amber-200 hover:bg-amber-50">
                View All Invoices
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sales Metrics */}
        <div className="lg:col-span-2">
          <Card className="shadow-sm border-0">
            <CardHeader>
              <CardTitle className="font-dm-serif text-techlocal-dark">Sales Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-500">Total Pipeline Value</span>
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight size={16} className="mr-1" />
                        <span className="text-xs">+12%</span>
                      </div>
                    </div>
                    <div className="text-2xl font-bold">{formatCurrency(totalPipeline)}</div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-500">Weighted Pipeline Value</span>
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight size={16} className="mr-1" />
                        <span className="text-xs">+8%</span>
                      </div>
                    </div>
                    <div className="text-2xl font-bold">{formatCurrency(weightedPipeline)}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-500">Total Invoiced</span>
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight size={16} className="mr-1" />
                        <span className="text-xs">+15%</span>
                      </div>
                    </div>
                    <div className="text-2xl font-bold">{formatCurrency(totalInvoiced)}</div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-500">Outstanding Payments</span>
                      <div className="flex items-center text-red-600">
                        <ArrowDownRight size={16} className="mr-1" />
                        <span className="text-xs">-5%</span>
                      </div>
                    </div>
                    <div className="text-2xl font-bold">{formatCurrency(totalOutstanding)}</div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium">Pipeline by Stage</h3>
                </div>
                <div className="h-48 flex items-end space-x-2">
                  <div className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-blue-200 rounded-t-sm" style={{ height: '30%' }}></div>
                    <span className="text-xs mt-2">Lead</span>
                  </div>
                  <div className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-blue-300 rounded-t-sm" style={{ height: '45%' }}></div>
                    <span className="text-xs mt-2">Qualified</span>
                  </div>
                  <div className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-blue-400 rounded-t-sm" style={{ height: '70%' }}></div>
                    <span className="text-xs mt-2">Proposal</span>
                  </div>
                  <div className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-blue-500 rounded-t-sm" style={{ height: '85%' }}></div>
                    <span className="text-xs mt-2">Negotiation</span>
                  </div>
                  <div className="flex-1 flex flex-col items-center">
                    <div className="w-full bg-blue-600 rounded-t-sm" style={{ height: '60%' }}></div>
                    <span className="text-xs mt-2">Closed Won</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="shadow-sm border-0">
          <CardHeader>
            <CardTitle className="font-dm-serif text-techlocal-dark">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No recent activities found
                </div>
              ) : recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="mt-0.5">
                    {activity.type === 'note' && (
                      <div className="p-2 bg-gray-100 rounded-full">
                        <FileText size={14} className="text-gray-600" />
                      </div>
                    )}
                    {activity.type === 'email' && (
                      <div className="p-2 bg-blue-100 rounded-full">
                        <FileText size={14} className="text-blue-600" />
                      </div>
                    )}
                    {activity.type === 'call' && (
                      <div className="p-2 bg-green-100 rounded-full">
                        <FileText size={14} className="text-green-600" />
                      </div>
                    )}
                    {activity.type === 'meeting' && (
                      <div className="p-2 bg-purple-100 rounded-full">
                        <FileText size={14} className="text-purple-600" />
                      </div>
                    )}
                    {activity.type === 'task' && (
                      <div className="p-2 bg-amber-100 rounded-full">
                        <FileText size={14} className="text-amber-600" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex justify-between">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <span className="text-xs text-gray-500">
                        {new Date(activity.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 line-clamp-2">{activity.description}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock size={12} className="mr-1" />
                      <span>{new Date(activity.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="ghost" className="w-full text-sm">View All Activity</Button>
            </div>
          </CardContent>
        </Card>
      </div>
      </>
      )}
    </CrmLayout>
  );
};

export default Dashboard;
