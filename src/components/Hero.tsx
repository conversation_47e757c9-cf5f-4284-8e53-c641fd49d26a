import { useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  CircleUser,
  Briefcase,
  Globe,
  Code,
  Laptop,
  Server,
  Monitor,
  Smartphone,
  HeartHandshake,
  Network,
  Share,
  Braces,
  BarChart,
} from 'lucide-react';

const Hero = () => {
  const iconDefs = [
    { Icon: Globe, size: 80, top: 10, left: 8 },
    { Icon: CircleUser, size: 70, top: 75, left: 25 },
    { Icon: Briefcase, size: 75, top: 15, left: 88 },
    { Icon: Code, size: 80, top: 92, left: 60 },
    { Icon: Laptop, size: 65, top: 43, left: 20 },
    { Icon: Server, size: 70, top: 5, left: 30 },
    { Icon: Monitor, size: 75, top: 35, left: 75 },
    { Icon: Smartphone, size: 60, top: 65, left: 60 },
    { Icon: HeartHandshake, size: 85, top: 20, left: 55 },
    { Icon: Network, size: 70, top: 65, left: 95 },
    { Icon: Share, size: 65, top: 80, left: 75 },
    { Icon: Braces, size: 75, top: 38, left: 40 },
    { Icon: BarChart, size: 80, top: 65, left: 3 },
  ];

  const wrapperRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const iconRefs = useRef<(HTMLDivElement | null)[]>([]);
  const icons = useRef<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    el: HTMLDivElement | null;
  }[]>([]);
  const confetti = useRef<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    life: number;
    color: string;
  }[]>([]);
  const frame = useRef<number>();

  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  const initIcons = () => {
    const wrap = wrapperRef.current;
    if (!wrap) return;
    const rect = wrap.getBoundingClientRect();
    icons.current = iconDefs.map((d, i) => ({
      x: (d.left / 100) * rect.width,
      y: (d.top / 100) * rect.height,
      vx: random(-0.3, 0.3),
      vy: random(-0.3, 0.3),
      size: d.size,
      el: iconRefs.current[i],
    }));
    icons.current.forEach((ic) => {
      if (ic.el) {
        ic.el.style.transform = `translate(${ic.x}px, ${ic.y}px)`;
      }
    });
  };

  const spawnConfetti = (x: number, y: number) => {
    for (let i = 0; i < 20; i++) {
      confetti.current.push({
        x,
        y,
        vx: random(-1, 1),
        vy: random(-1.5, -0.5),
        life: 60,
        color: `hsl(${Math.floor(random(0, 360))},80%,60%)`,
      });
    }
  };

  const updateConfetti = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.clearRect(0, 0, width, height);
    const parts = confetti.current;
    for (let i = parts.length - 1; i >= 0; i--) {
      const p = parts[i];
      p.x += p.vx;
      p.y += p.vy;
      p.vy += 0.02;
      p.life -= 1;
      ctx.fillStyle = p.color;
      ctx.globalAlpha = Math.max(p.life / 60, 0);
      ctx.fillRect(p.x, p.y, 3, 3);
      if (p.life <= 0) parts.splice(i, 1);
    }
    ctx.globalAlpha = 1;
  };

  const animate = () => {
    const wrap = wrapperRef.current;
    const canvas = canvasRef.current;
    if (!wrap || !canvas) return;
    const rect = wrap.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    canvas.width = rect.width;
    canvas.height = rect.height;

    icons.current.forEach((ic) => {
      ic.x += ic.vx;
      ic.y += ic.vy;
      if (ic.x < 0 || ic.x + ic.size > rect.width) ic.vx *= -1;
      if (ic.y < 0 || ic.y + ic.size > rect.height) ic.vy *= -1;
    });

    for (let i = 0; i < icons.current.length; i++) {
      for (let j = i + 1; j < icons.current.length; j++) {
        const a = icons.current[i];
        const b = icons.current[j];
        const dx = a.x + a.size / 2 - (b.x + b.size / 2);
        const dy = a.y + a.size / 2 - (b.y + b.size / 2);
        const dist = Math.hypot(dx, dy);
        const minDist = (a.size + b.size) / 2;
        if (dist < minDist) {
          const tempVx = a.vx;
          const tempVy = a.vy;
          a.vx = b.vx;
          a.vy = b.vy;
          b.vx = tempVx;
          b.vy = tempVy;
          spawnConfetti(a.x + dx / 2, a.y + dy / 2);
        }
      }
    }

    icons.current.forEach((ic) => {
      if (ic.el) ic.el.style.transform = `translate(${ic.x}px, ${ic.y}px)`;
    });

    updateConfetti(ctx, rect.width, rect.height);
    frame.current = requestAnimationFrame(animate);
  };

  useEffect(() => {
    initIcons();
    frame.current = requestAnimationFrame(animate);
    const debounced = (() => {
      let t: NodeJS.Timeout;
      return () => {
        clearTimeout(t);
        t = setTimeout(() => {
          initIcons();
        }, 200);
      };
    })();
    window.addEventListener('resize', debounced);
    return () => {
      if (frame.current) cancelAnimationFrame(frame.current);
      window.removeEventListener('resize', debounced);
    };
  }, []);

  return (
    <section id="hero" className="relative overflow-hidden pt-6 pb-16 md:pt-8 md:pb-24">
      <div className="section-container">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mt-0 mb-10 flex justify-center">
            <img
              src="/lovable-uploads/ad768163-427a-47fa-8859-c1c84d20a92a.png"
              alt="Tech Local Logo"
              className="h-12 md:h-20 w-auto"
            />
          </div>

          <div className="animate-fade-in space-y-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-dm-serif tracking-tight">
              Making connections
            </h1>

            <p className="text-lg md:text-xl max-w-2xl mx-auto">
              Connecting local businesses to their customers through smart,
              accessible web solutions. We make technology simple, so you can
              focus on what you do best.
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="#contact">
                <Button className="text-lg px-8 py-6 bg-techlocal-dark hover:bg-black text-white">
                  Get Started
                </Button>
              </a>
              <a href="#services">
                <Button variant="outline" className="text-lg px-8 py-6 border-techlocal-dark text-techlocal-dark hover:bg-techlocal-stone/20">
                  Our Services
                </Button>
              </a>
            </div>
          </div>
        </div>
      </div>

      <div ref={wrapperRef} className="absolute inset-0 -z-10 overflow-hidden">
        {iconDefs.map((icon, index) => (
          <div
            key={index}
            ref={(el) => (iconRefs.current[index] = el)}
            className="absolute opacity-10 will-change-transform pointer-events-none"
          >
            <icon.Icon
              size={icon.size}
              strokeWidth={1}
              className="text-techlocal-dark"
            />
          </div>
        ))}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none"
        />
      </div>
    </section>
  );
};

export default Hero;
