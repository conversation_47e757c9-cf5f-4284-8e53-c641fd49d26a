
import { MapPin, Users, Zap } from "lucide-react";

const AboutUs = () => {
  return (
    <section id="about" className="relative z-10 bg-white py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="section-title">About Us</h2>
          <p className="max-w-3xl mx-auto text-lg">
            Tech Local is dedicated to helping businesses in East London and beyond 
            establish their online presence and streamline operations with accessible, 
            affordable technology solutions.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-5xl mx-auto">
          <div className="p-6 rounded-lg text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-techlocal-beige inline-flex">
                <MapPin size={32} className="text-techlocal-dark" />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-3 font-montserrat">Locally Based</h3>
            <p>
              Proudly based in East London, South Africa, we understand the unique needs of 
              businesses in our community.
            </p>
          </div>

          <div className="p-6 rounded-lg text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-techlocal-beige inline-flex">
                <Users size={32} className="text-techlocal-dark" />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-3 font-montserrat">Experienced Team</h3>
            <p>
              With 30 years of combined development experience across various industries, 
              we bring expertise to every project.
            </p>
          </div>

          <div className="p-6 rounded-lg text-center">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-techlocal-beige inline-flex">
                <Zap size={32} className="text-techlocal-dark" />
              </div>
            </div>
            <h3 className="text-xl font-bold mb-3 font-montserrat">Smart Solutions</h3>
            <p>
              We leverage AI and efficient development strategies to make professional 
              websites and tools financially accessible.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUs;
