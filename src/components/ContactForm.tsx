import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { supabase, SUPABASE_URL, SUPABASE_ANON_KEY } from "@/integrations/supabase/client";

const formSchema = z.object({
  businessName: z.string().min(1, "Business name is required"),
  contactName: z.string().min(1, "Contact name is required"),
  contactNumber: z.string()
    .min(1, "Contact number is required")
    .regex(/^(?:\+\d{1,3})?\s?\(?\d{2,3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/,
      "Please enter a valid phone number"),
  email: z.string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  projectGoals: z.string().min(1, "Please describe your project goals"),
  currentWebsite: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const ContactForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      businessName: "",
      contactName: "",
      contactNumber: "",
      email: "",
      projectGoals: "",
      currentWebsite: ""
    }
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      // First, save the data to the database using the secure function
      const { error } = await supabase.rpc('submit_contact_form', {
        business_name: data.businessName,
        contact_name: data.contactName,
        contact_number: data.contactNumber,
        email: data.email,
        project_goals: data.projectGoals,
        current_website: data.currentWebsite || null
      });

      if (error) throw error;

      // Then, send the email notification using the edge function
      try {
        const notificationResponse = await fetch(
          `${SUPABASE_URL}/functions/v1/send-notification`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
            },
            body: JSON.stringify({
              formData: data
            })
          }
        );

        const responseData = await notificationResponse.json();

        if (!notificationResponse.ok) {
          console.error('Email notification failed:', responseData);
          // We don't throw here because the form data was saved successfully
        } else if (responseData.warning) {
          // Log warning but don't show to user since the form was submitted successfully
          console.warn('Email notification warning:', responseData.warning, responseData.error);
        }
      } catch (notificationError) {
        console.error('Error sending notification:', notificationError);
        // We don't throw here because the form data was saved successfully
      }

      // Show success message
      toast({
        title: "Request Submitted",
        description: "Thank you for reaching out. We'll be in touch within 24 hours.",
      });

      form.reset();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Submission Error",
        description: "There was an error submitting your request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="relative z-10 bg-white py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="section-title">Request a Service</h2>
            <p className="text-lg">
              Ready to take your business online or improve your web presence?
              Fill out the form below, and we'll get back to you within 24 hours.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="businessName"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">
                        Business Name <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Your Business Name"
                          className="border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactName"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">
                        Contact Name <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Your Name"
                          className="border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactNumber"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">
                        Contact Number <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Your Phone Number"
                          className="border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="font-medium">
                        Email Address <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          className="border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="currentWebsite"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="font-medium">
                      Current Website (if applicable)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="https://your-website.com"
                        className="border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="projectGoals"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="font-medium">
                      Project Goals/Interest <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Tell us about your project and what you're looking to achieve..."
                        className="min-h-32 border-techlocal-accent/50 focus-visible:ring-techlocal-dark"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-techlocal-dark hover:bg-black text-white py-6"
              >
                {isSubmitting ? "Submitting..." : "Submit Request"}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
