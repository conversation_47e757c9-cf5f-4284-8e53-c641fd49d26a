import { Globe, Briefcase, Computer } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const Services = () => {
  return (
    <section id="services" className="relative z-10 bg-techlocal-beige py-16 md:py-24 overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="section-title">Our Services</h2>
          <p className="max-w-3xl mx-auto text-lg">
            We take the complexity out of website setup and business operations, 
            making digital transformation simple and affordable.
          </p>
        </div>
        
        {/* Decorative background elements */}
        <div className="absolute top-1/4 left-1/4 w-48 h-48 rounded-full bg-techlocal-dark/5 blur-3xl opacity-60 -z-10 hidden md:block"></div>
        <div className="absolute bottom-1/3 right-1/4 w-64 h-64 rounded-full bg-techlocal-stone/20 blur-3xl opacity-50 -z-10 hidden md:block"></div>
        
        {/* Service cards container */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto relative">
          {/* Web Presence Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-techlocal-stone/20 to-transparent rounded-xl blur opacity-0 group-hover:opacity-70 transition-opacity duration-500 -z-10 transform scale-105"></div>
            <Card className="bg-white border-none shadow-lg group-hover:shadow-2xl transition-all duration-500 rounded-xl overflow-hidden h-full transform translate-y-0 group-hover:-translate-y-2">
              <CardHeader className="pb-2 relative">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-techlocal-beige to-techlocal-stone/20 rounded-bl-[80px] -z-10"></div>
                <div className="mb-4 w-16 h-16 rounded-full bg-techlocal-dark flex items-center justify-center transform transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="font-montserrat text-2xl text-techlocal-dark">Web Presence</CardTitle>
                <CardDescription className="text-techlocal-dark/80 font-medium">Stand out online</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="space-y-3 transition-all duration-300 group-hover:translate-x-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Professional website design</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Mobile-responsive layouts</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>SEO optimization</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Content creation</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Business Operations Card - Elevated */}
          <div className="group relative mt-0 md:-mt-4">
            <div className="absolute inset-0 bg-gradient-to-br from-techlocal-stone/20 to-transparent rounded-xl blur opacity-0 group-hover:opacity-70 transition-opacity duration-500 -z-10 transform scale-105"></div>
            <Card className="bg-white border-none shadow-lg group-hover:shadow-2xl transition-all duration-500 rounded-xl overflow-hidden h-full transform translate-y-0 group-hover:-translate-y-2">
              <CardHeader className="pb-2 relative">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-techlocal-beige to-techlocal-stone/20 rounded-bl-[80px] -z-10"></div>
                <div className="mb-4 w-16 h-16 rounded-full bg-techlocal-dark flex items-center justify-center transform transition-transform duration-300 group-hover:scale-110 group-hover:-rotate-12">
                  <Briefcase className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="font-montserrat text-2xl text-techlocal-dark">Business Operations</CardTitle>
                <CardDescription className="text-techlocal-dark/80 font-medium">Streamline your workflow</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="space-y-3 transition-all duration-300 group-hover:translate-x-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Customer management systems</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Appointment scheduling</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Inventory management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Payment processing</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
            <div className="absolute -bottom-2 -right-2 w-24 h-24 bg-techlocal-dark/5 rounded-full animate-float-slow hidden md:block -z-10"></div>
          </div>

          {/* AI Solutions Card */}
          <div className="group relative">
            <div className="absolute inset-0 bg-gradient-to-br from-techlocal-stone/20 to-transparent rounded-xl blur opacity-0 group-hover:opacity-70 transition-opacity duration-500 -z-10 transform scale-105"></div>
            <Card className="bg-white border-none shadow-lg group-hover:shadow-2xl transition-all duration-500 rounded-xl overflow-hidden h-full transform translate-y-0 group-hover:-translate-y-2">
              <CardHeader className="pb-2 relative">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-techlocal-beige to-techlocal-stone/20 rounded-bl-[80px] -z-10"></div>
                <div className="mb-4 w-16 h-16 rounded-full bg-techlocal-dark flex items-center justify-center transform transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">
                  <Computer className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="font-montserrat text-2xl text-techlocal-dark">AI Solutions</CardTitle>
                <CardDescription className="text-techlocal-dark/80 font-medium">Smart technology</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <ul className="space-y-3 transition-all duration-300 group-hover:translate-x-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Chatbots for customer service</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Automated content generation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Data analysis and insights</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-techlocal-dark font-bold">•</span>
                    <span>Personalized recommendations</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Additional decorative elements */}
        <div className="absolute bottom-10 left-10 w-32 h-32 border-2 border-techlocal-dark/10 rounded-full animate-pulse hidden md:block"></div>
        <div className="absolute top-1/3 right-10 w-16 h-16 border border-techlocal-dark/10 rounded-full animate-float-medium hidden md:block"></div>
      </div>
    </section>
  );
};

export default Services;
