import React from 'react';
import { Quote, Customer } from '@/types/crm';
import { quoteService } from '@/services';
import { pdf } from '@react-pdf/renderer';
import QuotePdfDocument from '@/components/crm/QuotePdfDocument';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/integrations/supabase/client';
import { companyInfo } from "@/lib/companyInfo";

/* ------------------- helper: blob → base64 ------------------------------ */
const blobToBase64 = (blob: Blob): Promise<string> =>
  new Promise((resolve) => {
    const r = new FileReader();
    r.onloadend = () => resolve((r.result as string).split(',')[1]);
    r.readAsDataURL(blob);
  });

export const quoteUtilsService = {
  /* expose company info to both preview & PDF */
  getCompanyInfo() {
    return companyInfo;
  },

  /* -------------------- PDF generation (react-pdf) ---------------------- */
  async generatePDF(
    _element: HTMLElement,           // kept for signature compatibility
    quote: Quote,
    customer: Customer
  ): Promise<string> {
    try {
      // Create the PDF document with React components
      // We need to use the Document component that's already included in QuotePdfDocument
      // This approach creates a smaller PDF with selectable text
      const element = React.createElement(QuotePdfDocument, {
        quote,
        customer,
        companyInfo
      });

      // React-pdf renders entirely in the browser; pdf().toBlob() is async
      // @ts-expect-error - The type definitions for pdf() are incorrect
      const blob = await pdf(element).toBlob();
      return await blobToBase64(blob);  // convert to base64 for storage/transmission
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF document');
    }
  },

  // Send quote via email
  async sendQuoteEmail(
    quote: Quote,
    customer: Customer,
    pdfBase64: string,
    customMessage?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(
        `${SUPABASE_URL}/functions/v1/send-quote`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${SUPABASE_ANON_KEY}`
          },
          body: JSON.stringify({
            emailData: {
              quoteId: quote.id,
              customerName: customer.name,
              customerEmail: customer.email,
              quoteTitle: quote.title,
              validUntil: quote.validUntil,
              pdfBase64: pdfBase64,
              quoteAmount: quote.amount,
              message: customMessage,
            }
          })
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        console.error("Error sending quote email:", data.error);
        return {
          success: false,
          message: "Failed to send quote email. Please try again."
        };
      }

      // If the quote is in draft status, update it to sent
      if (quote.status === "draft") {
        await quoteService.updateQuote(quote.id, { status: "sent" });
      }
      // If the quote is already sent, accepted, rejected, or expired, don't change its status
      // This ensures quotes remain immutable once they're out of draft

      return {
        success: true,
        message: "Quote has been sent successfully!"
      };
    } catch (error) {
      console.error("Error sending quote email:", error);
      return {
        success: false,
        message: "An error occurred while sending the quote. Please try again."
      };
    }
  },

  // Download quote as PDF
  async downloadQuotePDF(pdfBase64: string, quoteId: string, quote?: Quote): Promise<void> {
    const linkSource = `data:application/pdf;base64,${pdfBase64}`;
    const downloadLink = document.createElement("a");
    const fileName = `Quote-${quoteId.substring(0, 6).toUpperCase()}.pdf`;

    downloadLink.href = linkSource;
    downloadLink.download = fileName;
    downloadLink.click();

    // If quote is provided and it's in draft status, ask if it should be marked as sent
    if (quote && quote.status === 'draft') {
      // Use window.confirm for a simple confirmation dialog
      const shouldMarkAsSent = window.confirm(
        "This quote is currently in draft status. Would you like to mark it as sent?"
      );

      if (shouldMarkAsSent) {
        try {
          await quoteService.updateQuote(quote.id, { status: "sent" });
          return;
        } catch (error) {
          console.error("Error updating quote status:", error);
          throw new Error("Failed to update quote status");
        }
      }
    }
    // If quote is in any other status, don't change it
  }
};
