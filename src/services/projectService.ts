import { supabase } from "@/integrations/supabase/client";
import { Project } from "@/types/crm";

export const projectService = {
  // Get all projects
  async getProjects(): Promise<Project[]> {
    const { data, error } = await supabase
      .from("projects")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching projects:", error);
      throw error;
    }

    return data.map((project) => ({
      id: project.id,
      customerId: project.customer_id,
      dealId: project.deal_id,
      title: project.title || "Untitled Project",
      description: project.description || "",
      status: project.status as Project["status"],
      startDate: project.start_date,
      endDate: project.end_date,
      createdAt: project.created_at,
      updatedAt: project.updated_at,
      budget: project.budget,
      currentWebsite: project.current_website,
      goals: project.goals,
    }));
  },

  // Get project by ID
  async getProjectById(id: string): Promise<Project> {
    const { data, error } = await supabase
      .from("projects")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching project with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      dealId: data.deal_id,
      title: data.title || "Untitled Project",
      description: data.description || "",
      status: data.status as Project["status"],
      startDate: data.start_date,
      endDate: data.end_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      budget: data.budget,
      currentWebsite: data.current_website,
      goals: data.goals,
    };
  },

  // Get projects by customer ID
  async getProjectsByCustomerId(customerId: string): Promise<Project[]> {
    const { data, error } = await supabase
      .from("projects")
      .select("*")
      .eq("customer_id", customerId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching projects for customer ${customerId}:`, error);
      throw error;
    }

    return data.map((project) => ({
      id: project.id,
      customerId: project.customer_id,
      dealId: project.deal_id,
      title: project.title || "Untitled Project",
      description: project.description || "",
      status: project.status as Project["status"],
      startDate: project.start_date,
      endDate: project.end_date,
      createdAt: project.created_at,
      updatedAt: project.updated_at,
      budget: project.budget,
      currentWebsite: project.current_website,
      goals: project.goals,
    }));
  },

  // Create a new project
  async createProject(project: Omit<Project, "id" | "createdAt" | "updatedAt">): Promise<Project> {
    console.log("Creating project with data:", project);

    // Start a transaction to ensure all operations succeed or fail together
    const { data, error } = await supabase
      .from("projects")
      .insert({
        customer_id: project.customerId,
        deal_id: project.dealId,
        title: project.title,
        description: project.description,
        status: project.status,
        start_date: project.startDate,
        end_date: project.endDate,
        budget: project.budget,
        current_website: project.currentWebsite,
        goals: project.goals,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating project:", error);
      throw error;
    }

    try {
      // Update the customer status to active
      await supabase
        .from("customers")
        .update({ status: "active" })
        .eq("id", project.customerId);

      // If there's an associated deal, update it to closed-won
      if (project.dealId) {
        // Check if the deal is already closed-won
        const { data: dealData } = await supabase
          .from("deals")
          .select("stage")
          .eq("id", project.dealId)
          .single();

        // Only update if not already closed-won
        if (dealData && dealData.stage !== "closed-won") {
          await supabase
            .from("deals")
            .update({
              stage: "closed-won",
              probability: 100 // Set probability to 100% since it's won
            })
            .eq("id", project.dealId);
        }
      }
    } catch (relatedUpdateError) {
      console.error("Error updating related records:", relatedUpdateError);
      // We don't throw here because the project was successfully created
      // This is a non-critical error that shouldn't prevent the project creation
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      dealId: data.deal_id,
      title: data.title || "Untitled Project",
      description: data.description || "",
      status: data.status as Project["status"],
      startDate: data.start_date,
      endDate: data.end_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      budget: data.budget,
      currentWebsite: data.current_website,
      goals: data.goals,
    };
  },

  // Update an existing project
  async updateProject(id: string, project: Partial<Omit<Project, "id" | "createdAt" | "updatedAt">>): Promise<Project> {
    console.log("Updating project with ID", id, "and data:", project);

    // Get the current project data to track changes
    const { data: currentProject } = await supabase
      .from("projects")
      .select("*")
      .eq("id", id)
      .single();

    if (!currentProject) {
      throw new Error(`Project with ID ${id} not found`);
    }

    const updateData: any = {};

    if (project.customerId !== undefined) updateData.customer_id = project.customerId;
    if (project.dealId !== undefined) updateData.deal_id = project.dealId;
    if (project.title !== undefined) updateData.title = project.title;
    if (project.description !== undefined) updateData.description = project.description;
    if (project.status !== undefined) updateData.status = project.status;
    if (project.startDate !== undefined) updateData.start_date = project.startDate;
    if (project.endDate !== undefined) updateData.end_date = project.endDate;
    if (project.budget !== undefined) updateData.budget = project.budget;
    if (project.currentWebsite !== undefined) updateData.current_website = project.currentWebsite;
    if (project.goals !== undefined) updateData.goals = project.goals;

    const { data, error } = await supabase
      .from("projects")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating project with ID ${id}:`, error);
      throw error;
    }

    try {
      // If customer ID has changed, update the new customer's status to active
      if (project.customerId && project.customerId !== currentProject.customer_id) {
        await supabase
          .from("customers")
          .update({ status: "active" })
          .eq("id", project.customerId);
      }

      // If deal ID has changed, update the new deal to closed-won
      if (project.dealId && project.dealId !== currentProject.deal_id) {
        // Check if the deal is already closed-won
        const { data: dealData } = await supabase
          .from("deals")
          .select("stage")
          .eq("id", project.dealId)
          .single();

        // Only update if not already closed-won
        if (dealData && dealData.stage !== "closed-won") {
          await supabase
            .from("deals")
            .update({
              stage: "closed-won",
              probability: 100 // Set probability to 100% since it's won
            })
            .eq("id", project.dealId);
        }
      }
    } catch (relatedUpdateError) {
      console.error("Error updating related records:", relatedUpdateError);
      // We don't throw here because the project was successfully updated
      // This is a non-critical error that shouldn't prevent the project update
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      dealId: data.deal_id,
      title: data.title || "Untitled Project",
      description: data.description || "",
      status: data.status as Project["status"],
      startDate: data.start_date,
      endDate: data.end_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      budget: data.budget,
      currentWebsite: data.current_website,
      goals: data.goals,
    };
  },

  // Delete a project
  async deleteProject(id: string): Promise<void> {
    const { error } = await supabase
      .from("projects")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting project with ID ${id}:`, error);
      throw error;
    }
  },
};
