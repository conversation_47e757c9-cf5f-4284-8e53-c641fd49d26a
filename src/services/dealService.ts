import { supabase } from "@/integrations/supabase/client";
import { Deal } from "@/types/crm";

export const dealService = {
  // Get all deals
  async getDeals(): Promise<Deal[]> {
    const { data, error } = await supabase
      .from("deals")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching deals:", error);
      throw error;
    }

    return data.map((deal) => ({
      id: deal.id,
      customerId: deal.customer_id,
      title: deal.title,
      value: deal.value,
      stage: deal.stage,
      probability: deal.probability,
      expectedCloseDate: deal.expected_close_date,
      createdAt: deal.created_at,
      updatedAt: deal.updated_at,
      notes: deal.notes || "",
      assignedTo: deal.assigned_to,
    }));
  },

  // Get deal by ID
  async getDealById(id: string): Promise<Deal> {
    const { data, error } = await supabase
      .from("deals")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching deal with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      title: data.title,
      value: data.value,
      stage: data.stage,
      probability: data.probability,
      expectedCloseDate: data.expected_close_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      notes: data.notes || "",
      assignedTo: data.assigned_to,
    };
  },

  // Get deals by customer ID
  async getDealsByCustomerId(customerId: string): Promise<Deal[]> {
    const { data, error } = await supabase
      .from("deals")
      .select("*")
      .eq("customer_id", customerId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching deals for customer ${customerId}:`, error);
      throw error;
    }

    return data.map((deal) => ({
      id: deal.id,
      customerId: deal.customer_id,
      title: deal.title,
      value: deal.value,
      stage: deal.stage,
      probability: deal.probability,
      expectedCloseDate: deal.expected_close_date,
      createdAt: deal.created_at,
      updatedAt: deal.updated_at,
      notes: deal.notes || "",
      assignedTo: deal.assigned_to,
    }));
  },

  // Create a new deal
  async createDeal(deal: Omit<Deal, "id" | "createdAt" | "updatedAt">): Promise<Deal> {
    const { data, error } = await supabase
      .from("deals")
      .insert({
        customer_id: deal.customerId,
        title: deal.title,
        value: deal.value,
        stage: deal.stage,
        probability: deal.probability,
        expected_close_date: deal.expectedCloseDate,
        notes: deal.notes,
        assigned_to: deal.assignedTo,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating deal:", error);
      throw error;
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      title: data.title,
      value: data.value,
      stage: data.stage,
      probability: data.probability,
      expectedCloseDate: data.expected_close_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      notes: data.notes || "",
      assignedTo: data.assigned_to,
    };
  },

  // Update an existing deal
  async updateDeal(id: string, deal: Partial<Omit<Deal, "id" | "createdAt" | "updatedAt">>): Promise<Deal> {
    console.log("Updating deal with ID", id, "and data:", deal);

    // Check if the deal is being marked as closed-won
    const { data: currentDeal } = await supabase
      .from("deals")
      .select("stage")
      .eq("id", id)
      .single();

    const isBeingMarkedAsWon = currentDeal?.stage !== 'closed-won' && deal.stage === 'closed-won';
    const updateData: any = {};

    if (deal.customerId !== undefined) updateData.customer_id = deal.customerId;
    if (deal.title !== undefined) updateData.title = deal.title;
    if (deal.value !== undefined) updateData.value = deal.value;
    if (deal.stage !== undefined) updateData.stage = deal.stage;
    if (deal.probability !== undefined) updateData.probability = deal.probability;
    if (deal.expectedCloseDate !== undefined) updateData.expected_close_date = deal.expectedCloseDate;
    if (deal.notes !== undefined) updateData.notes = deal.notes;
    if (deal.assignedTo !== undefined) updateData.assigned_to = deal.assignedTo;

    // Update the deal
    const { data, error } = await supabase
      .from("deals")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating deal with ID ${id}:`, error);
      throw error;
    }

    // If the deal is being marked as won, create a project
    if (isBeingMarkedAsWon) {
      try {
        // Get the full deal data
        const { data: fullDeal } = await supabase
          .from("deals")
          .select("*, customers(*)")
          .eq("id", id)
          .single();

        if (fullDeal) {
          // Create a project from the deal
          const { error: projectError } = await supabase
            .from("projects")
            .insert({
              customer_id: fullDeal.customer_id,
              deal_id: fullDeal.id,
              title: `Project: ${fullDeal.title}`,
              description: fullDeal.notes || '',
              status: 'planning',  // Initial status for new projects
              goals: fullDeal.notes || 'Converted from deal',
              budget: fullDeal.value
            });

          if (projectError) {
            console.error("Error creating project from deal:", projectError);
            // We don't throw here because the deal update was successful
          }
        }
      } catch (projectCreationError) {
        console.error("Error in project creation process:", projectCreationError);
        // We don't throw here because the deal update was successful
      }
    }

    // Convert the data to our Deal type
    return {
      id: data.id,
      customerId: data.customer_id,
      title: data.title,
      value: data.value,
      stage: data.stage,
      probability: data.probability,
      expectedCloseDate: data.expected_close_date,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      notes: data.notes || "",
      assignedTo: data.assigned_to,
    };
  },

  // Delete a deal
  async deleteDeal(id: string): Promise<void> {
    const { error } = await supabase
      .from("deals")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting deal with ID ${id}:`, error);
      throw error;
    }
  },

  // Get deals by stage
  async getDealsByStage(stage: Deal['stage']): Promise<Deal[]> {
    const { data, error } = await supabase
      .from("deals")
      .select("*")
      .eq("stage", stage)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching deals with stage ${stage}:`, error);
      throw error;
    }

    return data.map((deal) => ({
      id: deal.id,
      customerId: deal.customer_id,
      title: deal.title,
      value: deal.value,
      stage: deal.stage,
      probability: deal.probability,
      expectedCloseDate: deal.expected_close_date,
      createdAt: deal.created_at,
      updatedAt: deal.updated_at,
      notes: deal.notes || "",
      assignedTo: deal.assigned_to,
    }));
  },

  // Get total deal value by stage
  async getTotalDealValueByStage(stage: Deal['stage']): Promise<number> {
    const { data, error } = await supabase
      .from("deals")
      .select("value")
      .eq("stage", stage);

    if (error) {
      console.error(`Error fetching total value for deals with stage ${stage}:`, error);
      throw error;
    }

    return data.reduce((total, deal) => total + (deal.value || 0), 0);
  },

  // Get total sales pipeline value
  async getTotalSalesPipelineValue(): Promise<number> {
    const { data, error } = await supabase
      .from("deals")
      .select("value, stage")
      .not("stage", "eq", "closed-lost");

    if (error) {
      console.error("Error fetching total sales pipeline value:", error);
      throw error;
    }

    return data.reduce((total, deal) => total + (deal.value || 0), 0);
  },

  // Get weighted sales pipeline value
  async getWeightedSalesPipelineValue(): Promise<number> {
    const { data, error } = await supabase
      .from("deals")
      .select("value, probability, stage")
      .not("stage", "eq", "closed-lost");

    if (error) {
      console.error("Error fetching weighted sales pipeline value:", error);
      throw error;
    }

    return data.reduce((total, deal) => total + ((deal.value || 0) * (deal.probability || 0) / 100), 0);
  },
};
