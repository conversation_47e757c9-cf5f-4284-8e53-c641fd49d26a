import { supabase } from "@/integrations/supabase/client";

export type AdminUser = {
  id: string;
  name: string | null;
  role: string;
  createdAt: string;
  updatedAt: string;
};

export const adminUserService = {
  // Get all admin users
  async getAdminUsers(): Promise<AdminUser[]> {
    const { data, error } = await supabase
      .from("admin_users")
      .select("*")
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching admin users:", error);
      throw error;
    }

    return data.map((user) => ({
      id: user.id,
      name: user.name,
      role: user.role,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    }));
  },

  // Get admin user by ID
  async getAdminUserById(id: string): Promise<AdminUser> {
    const { data, error } = await supabase
      .from("admin_users")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching admin user with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      role: data.role,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  // Get current admin user
  async getCurrentAdminUser(): Promise<AdminUser | null> {
    const { data: authData } = await supabase.auth.getUser();
    
    if (!authData.user) {
      return null;
    }

    try {
      const { data, error } = await supabase
        .from("admin_users")
        .select("*")
        .eq("id", authData.user.id)
        .single();

      if (error || !data) {
        console.error("Error fetching current admin user:", error);
        return null;
      }

      return {
        id: data.id,
        name: data.name,
        role: data.role,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      };
    } catch (error) {
      console.error("Error fetching current admin user:", error);
      return null;
    }
  },
};
