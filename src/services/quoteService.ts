import { supabase } from "@/integrations/supabase/client";
import { Quote, QuoteItem } from "@/types/crm";

export const quoteService = {
  // Get all quotes
  async getQuotes(): Promise<Quote[]> {
    const { data, error } = await supabase
      .from("quotes")
      .select("*, quote_items(*)")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching quotes:", error);
      throw error;
    }

    return data.map((quote) => ({
      id: quote.id,
      customerId: quote.customer_id,
      dealId: quote.deal_id,
      projectId: quote.project_id,
      title: quote.title,
      amount: quote.amount,
      status: quote.status,
      validUntil: quote.valid_until,
      createdAt: quote.created_at,
      updatedAt: quote.updated_at,
      parentQuoteId: quote.parent_quote_id,
      items: quote.quote_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    }));
  },

  // Get quote by ID
  async getQuoteById(id: string): Promise<Quote> {
    const { data, error } = await supabase
      .from("quotes")
      .select("*, quote_items(*)")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching quote with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      customerId: data.customer_id,
      dealId: data.deal_id,
      projectId: data.project_id,
      title: data.title,
      amount: data.amount,
      status: data.status,
      validUntil: data.valid_until,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      parentQuoteId: data.parent_quote_id,
      items: data.quote_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    };
  },

  // Get quotes by customer ID
  async getQuotesByCustomerId(customerId: string): Promise<Quote[]> {
    const { data, error } = await supabase
      .from("quotes")
      .select("*, quote_items(*)")
      .eq("customer_id", customerId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching quotes for customer ${customerId}:`, error);
      throw error;
    }

    return data.map((quote) => ({
      id: quote.id,
      customerId: quote.customer_id,
      dealId: quote.deal_id,
      projectId: quote.project_id,
      title: quote.title,
      amount: quote.amount,
      status: quote.status,
      validUntil: quote.valid_until,
      createdAt: quote.created_at,
      updatedAt: quote.updated_at,
      parentQuoteId: quote.parent_quote_id,
      items: quote.quote_items.map((item: any) => ({
        id: item.id,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        total: item.total,
      })),
    }));
  },

  // Create a new quote
  async createQuote(quote: Omit<Quote, "id" | "createdAt" | "updatedAt" | "amount">): Promise<Quote> {
    try {
      // Start a transaction
      const { data, error } = await supabase.rpc('create_quote', {
        p_customer_id: quote.customerId,
        p_deal_id: quote.dealId || null,
        p_project_id: quote.projectId || null,
        p_title: quote.title,
        p_status: quote.status,
        p_valid_until: quote.validUntil,
        p_items: quote.items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total: item.quantity * item.unitPrice
        }))
      });

      if (error) {
        console.error("Error creating quote with RPC:", error);
        throw error;
      }

      // Fetch the created quote with its items
      return await this.getQuoteById(data.id);
    } catch (error) {
      console.error("Error creating quote:", error);
      throw error;
    }
  },

  // Update an existing quote
  async updateQuote(id: string, quote: Partial<Omit<Quote, "id" | "createdAt" | "updatedAt" | "amount">>): Promise<Quote> {
    const updateData: any = {};

    if (quote.customerId !== undefined) updateData.customer_id = quote.customerId;
    if (quote.dealId !== undefined) updateData.deal_id = quote.dealId === null ? null : quote.dealId;
    if (quote.projectId !== undefined) updateData.project_id = quote.projectId === null ? null : quote.projectId;
    if (quote.title !== undefined) updateData.title = quote.title;
    if (quote.status !== undefined) updateData.status = quote.status;
    if (quote.validUntil !== undefined) updateData.valid_until = quote.validUntil;
    if (quote.parentQuoteId !== undefined) updateData.parent_quote_id = quote.parentQuoteId === null ? null : quote.parentQuoteId;

    // Update quote
    const { error: quoteError } = await supabase
      .from("quotes")
      .update(updateData)
      .eq("id", id);

    if (quoteError) {
      console.error(`Error updating quote with ID ${id}:`, quoteError);
      throw quoteError;
    }

    // Update quote items if provided
    if (quote.items && quote.items.length > 0) {
      // Delete existing items
      const { error: deleteError } = await supabase
        .from("quote_items")
        .delete()
        .eq("quote_id", id);

      if (deleteError) {
        console.error(`Error deleting quote items for quote ${id}:`, deleteError);
        throw deleteError;
      }

      // Insert new items
      const { error: insertError } = await supabase
        .from("quote_items")
        .insert(
          quote.items.map(item => ({
            quote_id: id,
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unitPrice,
            total: item.quantity * item.unitPrice
          }))
        );

      if (insertError) {
        console.error(`Error inserting quote items for quote ${id}:`, insertError);
        throw insertError;
      }
    }

    // Fetch the updated quote with its items
    return await this.getQuoteById(id);
  },

  // Create a new quote as a revision of an existing quote
  async createQuoteRevision(originalQuoteId: string, newStatus: Quote['status'] = 'draft'): Promise<Quote> {
    // Get the original quote
    const originalQuote = await this.getQuoteById(originalQuoteId);

    // Archive the original quote
    await this.archiveQuote(originalQuoteId);

    // Create a new quote based on the original
    const newQuoteData = {
      title: `${originalQuote.title} (Revised)`,
      customerId: originalQuote.customerId,
      dealId: originalQuote.dealId || null,
      projectId: originalQuote.projectId || null,
      status: newStatus,
      validUntil: originalQuote.validUntil,
      parentQuoteId: originalQuoteId,
      items: originalQuote.items.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.quantity * item.unitPrice
      }))
    };

    return await this.createQuote(newQuoteData);
  },

  // Archive a quote (mark as archived without deleting)
  async archiveQuote(id: string): Promise<Quote> {
    const { error } = await supabase
      .from("quotes")
      .update({ status: 'archived' })
      .eq("id", id);

    if (error) {
      console.error(`Error archiving quote with ID ${id}:`, error);
      throw error;
    }

    return await this.getQuoteById(id);
  },

  // Delete a quote
  async deleteQuote(id: string): Promise<void> {
    // Delete quote items first (should cascade, but just to be safe)
    const { error: itemsError } = await supabase
      .from("quote_items")
      .delete()
      .eq("quote_id", id);

    if (itemsError) {
      console.error(`Error deleting quote items for quote ${id}:`, itemsError);
      throw itemsError;
    }

    // Delete the quote
    const { error } = await supabase
      .from("quotes")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting quote with ID ${id}:`, error);
      throw error;
    }
  },

  // Add an item to a quote
  async addQuoteItem(quoteId: string, item: Omit<QuoteItem, "id">): Promise<QuoteItem> {
    const { data, error } = await supabase
      .from("quote_items")
      .insert({
        quote_id: quoteId,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total: item.total,
      })
      .select()
      .single();

    if (error) {
      console.error(`Error adding item to quote ${quoteId}:`, error);
      throw error;
    }

    return {
      id: data.id,
      description: data.description,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      total: data.total,
    };
  },

  // Update a quote item
  async updateQuoteItem(id: string, item: Partial<Omit<QuoteItem, "id">>): Promise<QuoteItem> {
    const updateData: any = {};

    if (item.description !== undefined) updateData.description = item.description;
    if (item.quantity !== undefined) updateData.quantity = item.quantity;
    if (item.unitPrice !== undefined) updateData.unit_price = item.unitPrice;
    if (item.total !== undefined) updateData.total = item.total;

    const { data, error } = await supabase
      .from("quote_items")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating quote item with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      description: data.description,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      total: data.total,
    };
  },

  // Delete a quote item
  async deleteQuoteItem(id: string): Promise<void> {
    const { error } = await supabase
      .from("quote_items")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting quote item with ID ${id}:`, error);
      throw error;
    }
  },
};
