import React from 'react';
import { Invoice, Customer, getInvoiceNumber } from '@/types/crm';
import { invoiceService } from '@/services';
import { pdf } from '@react-pdf/renderer';
import InvoicePdfDocument from '@/components/crm/InvoicePdfDocument';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/integrations/supabase/client';
import { companyInfo } from "@/lib/companyInfo";

export const invoiceUtilsService = {
  // Get company info
  getCompanyInfo() {
    return companyInfo;
  },

  /* -------------------- PDF generation (react-pdf) ---------------------- */
  async generatePDF(
    _element: HTMLElement,           // kept for signature compatibility
    invoice: Invoice,
    customer: Customer
  ): Promise<string> {
    try {
      // Create the PDF document with React components
      const element = React.createElement(InvoicePdfDocument, {
        invoice,
        customer,
        companyInfo
      });

      // Generate PDF
      const pdfBlob = await pdf(element).toBlob();

      // Convert to base64
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          if (typeof reader.result === 'string') {
            // Remove the data URL prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
          } else {
            reject(new Error('Failed to convert PDF to base64'));
          }
        };
        reader.onerror = reject;
        reader.readAsDataURL(pdfBlob);
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  },

  // Download invoice as PDF
  async downloadInvoicePDF(pdfBase64: string, invoiceId: string, invoice?: Invoice): Promise<void> {
    const linkSource = `data:application/pdf;base64,${pdfBase64}`;
    const downloadLink = document.createElement("a");
    const fileName = `Invoice-${getInvoiceNumber(invoiceId)}.pdf`;

    downloadLink.href = linkSource;
    downloadLink.download = fileName;
    downloadLink.click();

    // If invoice is provided and it's in draft status, ask if it should be marked as sent
    if (invoice && invoice.status === 'draft') {
      // Use window.confirm for a simple confirmation dialog
      const shouldMarkAsSent = window.confirm(
        "This invoice is currently in draft status. Would you like to mark it as sent?"
      );

      if (shouldMarkAsSent) {
        try {
          await invoiceService.updateInvoice(invoice.id, { status: "sent" });
          return;
        } catch (error) {
          console.error("Error updating invoice status:", error);
          throw new Error("Failed to update invoice status");
        }
      }
    }
    // If invoice is in any other status, don't change it
  },

  // Send invoice via email
  async sendInvoiceEmail(
    invoice: Invoice,
    customer: Customer,
    pdfBase64: string,
    message: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Call the Supabase Edge Function to send the email
      const response = await fetch(`${SUPABASE_URL}/functions/v1/send-invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          invoice: {
            id: invoice.id,
            number: invoice.invoiceNumber,
            title: invoice.title,
            amount: invoice.amount,
            dueDate: invoice.dueDate,
            status: invoice.status
          },
          customer: {
            name: customer.name,
            email: customer.email,
            contactName: customer.contactName
          },
          pdfBase64,
          message
        })
      });

      const result = await response.json();

      // If the invoice is in draft status, update it to sent
      if (invoice.status === "draft") {
        await invoiceService.updateInvoice(invoice.id, { status: "sent" });
      }
      // If the invoice is already sent, paid, overdue, or cancelled, don't change its status
      // This ensures invoices remain immutable once they're out of draft

      return result;
    } catch (error) {
      console.error('Error sending invoice email:', error);
      return {
        success: false,
        message: 'Failed to send email. Please try again.'
      };
    }
  }
};
