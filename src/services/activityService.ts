import { supabase } from "@/integrations/supabase/client";
import { Activity } from "@/types/crm";

export const activityService = {
  // Get all activities
  async getActivities(): Promise<Activity[]> {
    const { data, error } = await supabase
      .from("activities")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching activities:", error);
      throw error;
    }

    return data.map((activity) => ({
      id: activity.id,
      type: activity.type,
      title: activity.title,
      description: activity.description || "",
      relatedTo: {
        type: activity.related_type,
        id: activity.related_id,
      },
      createdAt: activity.created_at,
      createdBy: activity.created_by,
      dueDate: activity.due_date,
      completed: activity.completed,
    }));
  },

  // Get activity by ID
  async getActivityById(id: string): Promise<Activity> {
    const { data, error } = await supabase
      .from("activities")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching activity with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      type: data.type,
      title: data.title,
      description: data.description || "",
      relatedTo: {
        type: data.related_type,
        id: data.related_id,
      },
      createdAt: data.created_at,
      createdBy: data.created_by,
      dueDate: data.due_date,
      completed: data.completed,
    };
  },

  // Get activities by related entity
  async getActivitiesByRelatedEntity(type: 'customer' | 'deal' | 'project' | 'quote' | 'invoice', id: string): Promise<Activity[]> {
    const { data, error } = await supabase
      .from("activities")
      .select("*")
      .eq("related_type", type)
      .eq("related_id", id)
      .order("created_at", { ascending: false });

    if (error) {
      console.error(`Error fetching activities for ${type} ${id}:`, error);
      throw error;
    }

    return data.map((activity) => ({
      id: activity.id,
      type: activity.type,
      title: activity.title,
      description: activity.description || "",
      relatedTo: {
        type: activity.related_type,
        id: activity.related_id,
      },
      createdAt: activity.created_at,
      createdBy: activity.created_by,
      dueDate: activity.due_date,
      completed: activity.completed,
    }));
  },

  // Get recent activities
  async getRecentActivities(limit: number = 10): Promise<Activity[]> {
    const { data, error } = await supabase
      .from("activities")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching recent activities:", error);
      throw error;
    }

    return data.map((activity) => ({
      id: activity.id,
      type: activity.type,
      title: activity.title,
      description: activity.description || "",
      relatedTo: {
        type: activity.related_type,
        id: activity.related_id,
      },
      createdAt: activity.created_at,
      createdBy: activity.created_by,
      dueDate: activity.due_date,
      completed: activity.completed,
    }));
  },

  // Create a new activity
  async createActivity(activity: Omit<Activity, "id" | "createdAt">): Promise<Activity> {
    const { data, error } = await supabase
      .from("activities")
      .insert({
        type: activity.type,
        title: activity.title,
        description: activity.description,
        related_type: activity.relatedTo.type,
        related_id: activity.relatedTo.id,
        created_by: activity.createdBy,
        due_date: activity.dueDate,
        completed: activity.completed,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating activity:", error);
      throw error;
    }

    return {
      id: data.id,
      type: data.type,
      title: data.title,
      description: data.description || "",
      relatedTo: {
        type: data.related_type,
        id: data.related_id,
      },
      createdAt: data.created_at,
      createdBy: data.created_by,
      dueDate: data.due_date,
      completed: data.completed,
    };
  },

  // Update an existing activity
  async updateActivity(id: string, activity: Partial<Omit<Activity, "id" | "createdAt" | "createdBy">>): Promise<Activity> {
    const updateData: any = {};
    
    if (activity.type !== undefined) updateData.type = activity.type;
    if (activity.title !== undefined) updateData.title = activity.title;
    if (activity.description !== undefined) updateData.description = activity.description;
    if (activity.relatedTo !== undefined) {
      updateData.related_type = activity.relatedTo.type;
      updateData.related_id = activity.relatedTo.id;
    }
    if (activity.dueDate !== undefined) updateData.due_date = activity.dueDate;
    if (activity.completed !== undefined) updateData.completed = activity.completed;

    const { data, error } = await supabase
      .from("activities")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating activity with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      type: data.type,
      title: data.title,
      description: data.description || "",
      relatedTo: {
        type: data.related_type,
        id: data.related_id,
      },
      createdAt: data.created_at,
      createdBy: data.created_by,
      dueDate: data.due_date,
      completed: data.completed,
    };
  },

  // Delete an activity
  async deleteActivity(id: string): Promise<void> {
    const { error } = await supabase
      .from("activities")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting activity with ID ${id}:`, error);
      throw error;
    }
  },
};
