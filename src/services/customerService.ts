import { supabase } from "@/integrations/supabase/client";
import { Customer } from "@/types/crm";

export const customerService = {
  // Get all customers
  async getCustomers(): Promise<Customer[]> {
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching customers:", error);
      throw error;
    }

    return data.map((customer) => ({
      id: customer.id,
      name: customer.name,
      contactName: customer.contact_name,
      contactNumber: customer.contact_number,
      email: customer.email,
      address: customer.address || "",
      createdAt: customer.created_at,
      updatedAt: customer.updated_at,
      status: customer.status || "lead",
      notes: customer.notes || "",
    }));
  },

  // Get customer by ID
  async getCustomerById(id: string): Promise<Customer> {
    const { data, error } = await supabase
      .from("customers")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      console.error(`Error fetching customer with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      contactName: data.contact_name,
      contactNumber: data.contact_number,
      email: data.email,
      address: data.address || "",
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      status: data.status || "lead",
      notes: data.notes || "",
    };
  },

  // Create a new customer
  async createCustomer(customer: Omit<Customer, "id" | "createdAt" | "updatedAt">): Promise<Customer> {
    const { data, error } = await supabase
      .from("customers")
      .insert({
        name: customer.name,
        contact_name: customer.contactName,
        contact_number: customer.contactNumber,
        email: customer.email,
        address: customer.address,
        status: customer.status,
        notes: customer.notes,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating customer:", error);
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      contactName: data.contact_name,
      contactNumber: data.contact_number,
      email: data.email,
      address: data.address || "",
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      status: data.status,
      notes: data.notes || "",
    };
  },

  // Update an existing customer
  async updateCustomer(id: string, customer: Partial<Omit<Customer, "id" | "createdAt" | "updatedAt">>): Promise<Customer> {
    const updateData: any = {};
    
    if (customer.name !== undefined) updateData.name = customer.name;
    if (customer.contactName !== undefined) updateData.contact_name = customer.contactName;
    if (customer.contactNumber !== undefined) updateData.contact_number = customer.contactNumber;
    if (customer.email !== undefined) updateData.email = customer.email;
    if (customer.address !== undefined) updateData.address = customer.address;
    if (customer.status !== undefined) updateData.status = customer.status;
    if (customer.notes !== undefined) updateData.notes = customer.notes;

    const { data, error } = await supabase
      .from("customers")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating customer with ID ${id}:`, error);
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      contactName: data.contact_name,
      contactNumber: data.contact_number,
      email: data.email,
      address: data.address || "",
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      status: data.status,
      notes: data.notes || "",
    };
  },

  // Delete a customer
  async deleteCustomer(id: string): Promise<void> {
    const { error } = await supabase
      .from("customers")
      .delete()
      .eq("id", id);

    if (error) {
      console.error(`Error deleting customer with ID ${id}:`, error);
      throw error;
    }
  },
};
