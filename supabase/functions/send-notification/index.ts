// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

interface ContactFormData {
  businessName: string;
  contactName: string;
  contactNumber: string;
  email: string;
  projectGoals: string;
  currentWebsite?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { formData } = await req.json();

    if (!formData) {
      return new Response(
        JSON.stringify({ error: "No form data provided" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const data = formData as ContactFormData;

    // Construct email content
    const emailContent = `
      New Contact Form Submission

      Business Name: ${data.businessName}
      Contact Name: ${data.contactName}
      Contact Number: ${data.contactNumber}
      Email: ${data.email}

      Project Goals:
      ${data.projectGoals}

      Current Website: ${data.currentWebsite || "Not provided"}

      Submitted at: ${new Date().toISOString()}
    `;

    // Check if we have the API key
    const apiKey = Deno.env.get("RESEND_API_KEY");
    if (!apiKey) {
      console.error("Missing RESEND_API_KEY environment variable");

      // For now, let's return success anyway since the form data was saved
      return new Response(
        JSON.stringify({
          success: true,
          warning: "Email notification not sent: Missing API key"
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    try {
      // Send email using Resend
      const emailResponse = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: "Tech Local <<EMAIL>>",
          to: "<EMAIL>",
          subject: "New Contact Form Submission - Tech Local",
          text: emailContent,
        }),
      });

      if (!emailResponse.ok) {
        const errorText = await emailResponse.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
        } catch {
          errorJson = { raw: errorText };
        }
        console.error("Email sending failed:", errorJson);

        // Return success with warning since the form data was saved
        return new Response(
          JSON.stringify({
            success: true,
            warning: "Form submitted but email notification failed",
            error: errorJson
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }
    } catch (emailError) {
      console.error("Exception sending email:", emailError);

      // Return success with warning since the form data was saved
      return new Response(
        JSON.stringify({
          success: true,
          warning: "Form submitted but email notification failed",
          error: emailError.message
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    return new Response(
      JSON.stringify({ success: true }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Error processing request:", error);

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
