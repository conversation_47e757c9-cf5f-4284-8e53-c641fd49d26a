// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

interface QuoteEmailData {
  quoteId: string;
  customerName: string;
  customerEmail: string;
  quoteTitle: string;
  validUntil: string;
  quoteAmount: number;
  pdfBase64: string;
  message?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { emailData } = await req.json();

    if (!emailData) {
      return new Response(
        JSON.stringify({ error: "No email data provided" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    const data = emailData as QuoteEmailData;

    // Check if we have the API key
    const apiKey = Deno.env.get("RESEND_API_KEY");
    if (!apiKey) {
      console.error("Missing RESEND_API_KEY environment variable");
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing API key configuration"
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Default message if not provided
    const depositAmount = (data.quoteAmount ?? 0) / 2;
    const formatter = new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
    });
    const depositFormatted = formatter.format(depositAmount);

    const message = data.message || `Dear ${data.customerName},

Please find attached your quote for ${data.quoteTitle}.

The quote is valid until ${new Date(data.validUntil).toLocaleDateString()}.

Please note that a 50% deposit of ${depositFormatted} is required for work to begin.

Please let us know if you have any questions.

Best regards,
Tech Local Team`;

    try {
      // Send email using Resend
      const emailResponse = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: "Tech Local <<EMAIL>>",
          to: data.customerEmail,
          subject: `Quote: ${data.quoteTitle} - Tech Local`,
          text: message,
          attachments: [
            {
              filename: `Quote-${data.quoteId.substring(0, 6).toUpperCase()}.pdf`,
              content: data.pdfBase64,
            },
          ],
        }),
      });

      if (!emailResponse.ok) {
        const errorText = await emailResponse.text();
        let errorJson;
        try {
          errorJson = JSON.parse(errorText);
        } catch {
          errorJson = { raw: errorText };
        }
        console.error("Email sending failed:", errorJson);

        return new Response(
          JSON.stringify({
            success: false,
            error: errorJson
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" }
          }
        );
      }

      const responseData = await emailResponse.json();
      
      return new Response(
        JSON.stringify({ 
          success: true,
          id: responseData.id
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (emailError) {
      console.error("Exception sending email:", emailError);

      return new Response(
        JSON.stringify({
          success: false,
          error: emailError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
