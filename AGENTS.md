# AGENT instructions

These guidelines apply to all code in this repository.

## Commit guidelines
- Use short commit messages prefixed with a scope such as `feat:`, `fix:`, or `docs:`.
- Keep commits focused and avoid mixing unrelated changes.

## Coding style
- Use 2 spaces for indentation.
- Prefer single quotes for strings.
- Use camelCase for variables and functions.
- React component names should be PascalCase.
- Keep lines under 100 characters when possible.

## Programmatic checks
- Before committing, run `npm run lint` and `npm run build`.
- If linting fails due to pre-existing errors, ensure your changes do not introduce new violations.
- Document any failure due to environment restrictions in the PR notes.
